// main: ../main.scss
/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 20px 0;
  transition: all 0.5s;
  z-index: 997;

  .logo {
    line-height: 1;

    img {
      max-height: 32px;
      margin-right: 8px;
    }

    h1 {
      font-size: 30px;
      margin: 0;
      font-weight: 300;
      color: var(--heading-color);
    }
  }

  .scrolled & {
    box-shadow: 0px 0 18px rgba(0, 0, 0, 0.1);
  }
}