/**************************************
Section
**************************************/

#panel
{
	Float: left;
	Margin: 0 auto;
	Background: ;
	border-right: 5px solid steelblue;
	height: 100%;
	padding:10px;
}

#panel ul
{
	padding: 10px;
	font-size: 14px;
	color: steelblue;
}

#panel li
{
 	padding: 10px;
}
#panel li:hover
{
 	background-color:rgb(48, 119, 178);
}

#main-panel
{
    margin:0 auto;
    width:980px;
    padding:10px;
    height: 100%;
	overflow: scroll;
}

#cp-content
{
 padding: 20px;
 border-bottom: 1px dotted lightgray;
 border-top: 1px dotted lightgray;
 width: 980px;
 height: 503px;
 overflow: scroll;
}
/*****************************************
Header
******************************************/

#menu 
{
    background-color: #7FC8BA;
    height: 52px;
    margin: 0;
    border-bottom: 0px solid steelblue;
 
}
#menu a 
{
    color: #7FC8BA;
    font-weight: normal;
    list-style-type: none;
}

#menu a:hover
{
 color: white;
}

#menu #wrapper 
{
    height: 24px;
    margin: 0 auto;
    max-width: 990px;
    padding: 0;
    z-index: -5;
}
#menu ul li a img 
{
	width: 24px;
}
#menu ul li a 
{
	color: white;
}


/*********************************************
 Footer
**********************************************/

#footer 
{
    border-top: 0px solid orange;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width:100%;
    padding: 20px;
    clear: both;
    background: steelblue;
}
