/* Portfolio Sliding Showcase Styles */

.portfolio-slideshow {
  overflow: hidden;
  position: relative;
  background: #f8f9fa;
  padding: 80px 0;
  min-height: 600px;
}

.portfolio-slideshow .section-title {
  text-align: center;
  margin-bottom: 60px;
}

.portfolio-slideshow .section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d465e;
  margin-bottom: 20px;
}

.portfolio-slideshow .section-title p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Slideshow Container */
.slideshow-container {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Sliding Track */
.slideshow-track {
  display: flex;
  width: calc(300px * 18); /* Width for all items including duplicates */
  animation: slide 30s linear infinite;
  gap: 20px;
  will-change: transform; /* Optimize for animations */
}

/* Individual Slide Items */
.slideshow-item {
  flex: 0 0 280px;
  height: 360px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: #fff;
}

.slideshow-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
  z-index: 10;
}

.slideshow-item img {
  width: 100%;
  height: 240px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.slideshow-item:hover img {
  transform: scale(1.1);
}

/* Item Info Overlay */
.slideshow-item-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.slideshow-item:hover .slideshow-item-info {
  transform: translateY(0);
}

.slideshow-item-info h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #fff;
}

.slideshow-item-info p {
  font-size: 0.9rem;
  margin-bottom: 12px;
  opacity: 0.9;
}

.slideshow-item-info .portfolio-links {
  display: flex;
  gap: 10px;
}

.slideshow-item-info .portfolio-links a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.slideshow-item-info .portfolio-links a:hover {
  background: #fa2133;
  transform: scale(1.1);
}

/* Keyframe Animation */
@keyframes slide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-300px * 9)); /* Move by half the items */
  }
}

/* Pause animation on hover */
.slideshow-container:hover .slideshow-track {
  animation-play-state: paused;
}

/* Category Filter Badges */
.category-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-app .category-badge {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.filter-web .category-badge {
  background: linear-gradient(45deg, #f093fb, #f5576c);
  color: white;
}

.filter-card .category-badge {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio-slideshow {
    padding: 60px 0;
  }

  .slideshow-container {
    height: 300px;
  }

  .slideshow-item {
    flex: 0 0 220px;
    height: 280px;
  }

  .slideshow-item img {
    height: 180px;
  }

  .slideshow-track {
    width: calc(240px * 18);
    gap: 15px;
  }

  @keyframes slide {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-240px * 9));
    }
  }
}

@media (max-width: 480px) {
  .slideshow-item {
    flex: 0 0 180px;
    height: 240px;
  }

  .slideshow-item img {
    height: 140px;
  }

  .slideshow-track {
    width: calc(200px * 18);
    gap: 10px;
  }

  @keyframes slide {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-200px * 9));
    }
  }
}

/* Loading States */
.slideshow-container.loading {
  opacity: 0.7;
}

.slideshow-container.loading .slideshow-track {
  animation-play-state: paused;
}

.slideshow-container.loaded {
  opacity: 1;
  transition: opacity 0.5s ease;
}

.slideshow-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: #666;
}

.slideshow-loading::after {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #fa2133;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility improvements */
.slideshow-container:focus {
  outline: 2px solid #fa2133;
  outline-offset: 4px;
}

/* Smooth image loading */
.slideshow-item img {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.slideshow-item img.loaded,
.slideshow-container.loaded .slideshow-item img {
  opacity: 1;
}
