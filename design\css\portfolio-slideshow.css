/* Portfolio Sliding Showcase Styles */

.portfolio-slideshow {
  overflow: hidden;
  position: relative;
  background: #f8f9fa;
  padding: 80px 0;
  min-height: 600px;
}

.portfolio-slideshow .section-title {
  text-align: center;
  margin-bottom: 60px;
}

.portfolio-slideshow .section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d465e;
  margin-bottom: 20px;
}

.portfolio-slideshow .section-title p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Slideshow Container */
.slideshow-container {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 15px;
}

/* Sliding Track */
.slideshow-track {
  display: flex;
  width: calc(
    300px * 16
  ); /* Width for all items including duplicates (8 items × 2) */
  animation: slide 30s linear infinite;
  gap: 20px;
  will-change: transform; /* Optimize for animations */
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Prevent flickering */
}

/* Individual Slide Items */
.slideshow-item {
  flex: 0 0 280px;
  height: 360px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  cursor: pointer;
}

.slideshow-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0);
  transition: background 0.3s ease;
  z-index: 1;
  pointer-events: none;
}

.slideshow-item:hover::before {
  background: rgba(0, 0, 0, 0.2);
}

.slideshow-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Hidden overlay - will be replaced with modal */
.slideshow-item-info {
  display: none;
}

/* Portfolio Modal Styles */
.portfolio-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-modal.active {
  display: flex;
  opacity: 1;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.portfolio-modal-content {
  background: white;
  border-radius: 15px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.8) translateY(50px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.portfolio-modal-image {
  width: 100%;
  max-height: 70vh;
  object-fit: contain;
  background: #f8f9fa;
}

.portfolio-modal-info {
  padding: 30px;
  text-align: center;
}

.portfolio-modal-info h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d465e;
  margin-bottom: 15px;
}

.portfolio-modal-info p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.portfolio-modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.portfolio-modal-close:hover {
  background: #fa2133;
}

/* Responsive modal */
@media (max-width: 768px) {
  .portfolio-modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .portfolio-modal-info {
    padding: 20px;
  }

  .portfolio-modal-info h3 {
    font-size: 1.5rem;
  }

  .portfolio-modal-info p {
    font-size: 1rem;
  }
}

/* Keyframe Animation */
@keyframes slide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(
      calc(-300px * 8)
    ); /* Move by exactly 8 items for seamless loop */
  }
}

/* Keep animation running continuously */
.slideshow-container:hover .slideshow-track {
  animation-play-state: running;
}

/* Category Filter Badges */
.category-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-app .category-badge {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.filter-web .category-badge {
  background: linear-gradient(45deg, #f093fb, #f5576c);
  color: white;
}

.filter-card .category-badge {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio-slideshow {
    padding: 60px 0;
  }

  .slideshow-container {
    height: 300px;
  }

  .slideshow-item {
    flex: 0 0 220px;
    height: 280px;
  }

  .slideshow-item img {
    height: 100%;
  }

  .slideshow-track {
    width: calc(240px * 16);
    gap: 15px;
  }

  @keyframes slide {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-240px * 8));
    }
  }
}

@media (max-width: 480px) {
  .slideshow-item {
    flex: 0 0 180px;
    height: 240px;
  }

  .slideshow-item img {
    height: 100%;
  }

  .slideshow-track {
    width: calc(200px * 16);
    gap: 10px;
  }

  @keyframes slide {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-200px * 8));
    }
  }
}

/* Loading States */
.slideshow-container.loading {
  opacity: 0.7;
}

.slideshow-container.loading .slideshow-track {
  animation-play-state: running;
}

.slideshow-container.loaded {
  opacity: 1;
  transition: opacity 0.5s ease;
}

.slideshow-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: #666;
}

.slideshow-loading::after {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #fa2133;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility improvements */
.slideshow-container:focus {
  outline: none;
}

/* Smooth image loading */
.slideshow-item img {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.slideshow-item img.loaded,
.slideshow-container.loaded .slideshow-item img {
  opacity: 1;
}
