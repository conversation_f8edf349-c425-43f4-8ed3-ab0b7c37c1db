{"version": 3, "file": "button.js", "sources": ["../src/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_BUTTON = 'btn'\nconst CLASS_NAME_FOCUS = 'focus'\n\nconst SELECTOR_DATA_TOGGLE_CARROT = '[data-toggle^=\"button\"]'\nconst SELECTOR_DATA_TOGGLES = '[data-toggle=\"buttons\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\nconst SELECTOR_DATA_TOGGLES_BUTTONS = '[data-toggle=\"buttons\"] .btn'\nconst SELECTOR_INPUT = 'input:not([type=\"hidden\"])'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_BUTTON = '.btn'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_FOCUS_BLUR_DATA_API = `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                          `blur${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    this.shouldAvoidTriggerChange = false\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    let triggerChangeEvent = true\n    let addAriaPressed = true\n    const rootElement = $(this._element).closest(SELECTOR_DATA_TOGGLES)[0]\n\n    if (rootElement) {\n      const input = this._element.querySelector(SELECTOR_INPUT)\n\n      if (input) {\n        if (input.type === 'radio') {\n          if (input.checked && this._element.classList.contains(CLASS_NAME_ACTIVE)) {\n            triggerChangeEvent = false\n          } else {\n            const activeElement = rootElement.querySelector(SELECTOR_ACTIVE)\n\n            if (activeElement) {\n              $(activeElement).removeClass(CLASS_NAME_ACTIVE)\n            }\n          }\n        }\n\n        if (triggerChangeEvent) {\n          // if it's not a radio button or checkbox don't add a pointless/invalid checked property to the input\n          if (input.type === 'checkbox' || input.type === 'radio') {\n            input.checked = !this._element.classList.contains(CLASS_NAME_ACTIVE)\n          }\n\n          if (!this.shouldAvoidTriggerChange) {\n            $(input).trigger('change')\n          }\n        }\n\n        input.focus()\n        addAriaPressed = false\n      }\n    }\n\n    if (!(this._element.hasAttribute('disabled') || this._element.classList.contains('disabled'))) {\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed', !this._element.classList.contains(CLASS_NAME_ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static _jQueryInterface(config, avoidTriggerChange) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      data.shouldAvoidTriggerChange = avoidTriggerChange\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE_CARROT, event => {\n    let button = event.target\n    const initialButton = button\n\n    if (!$(button).hasClass(CLASS_NAME_BUTTON)) {\n      button = $(button).closest(SELECTOR_BUTTON)[0]\n    }\n\n    if (!button || button.hasAttribute('disabled') || button.classList.contains('disabled')) {\n      event.preventDefault() // work around Firefox bug #1540995\n    } else {\n      const inputBtn = button.querySelector(SELECTOR_INPUT)\n\n      if (inputBtn && (inputBtn.hasAttribute('disabled') || inputBtn.classList.contains('disabled'))) {\n        event.preventDefault() // work around Firefox bug #1540995\n        return\n      }\n\n      if (initialButton.tagName === 'INPUT' || button.tagName !== 'LABEL') {\n        Button._jQueryInterface.call($(button), 'toggle', initialButton.tagName === 'INPUT')\n      }\n    }\n  })\n  .on(EVENT_FOCUS_BLUR_DATA_API, SELECTOR_DATA_TOGGLE_CARROT, event => {\n    const button = $(event.target).closest(SELECTOR_BUTTON)[0]\n    $(button).toggleClass(CLASS_NAME_FOCUS, /^focus(in)?$/.test(event.type))\n  })\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  // ensure correct active class is set to match the controls' actual values/states\n\n  // find all checkboxes/readio buttons inside data-toggle groups\n  let buttons = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLES_BUTTONS))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    const input = button.querySelector(SELECTOR_INPUT)\n    if (input.checked || input.hasAttribute('checked')) {\n      button.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      button.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // find all button toggles\n  buttons = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    if (button.getAttribute('aria-pressed') === 'true') {\n      button.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      button.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Button._jQueryInterface\n$.fn[NAME].Constructor = Button\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Button._jQueryInterface\n}\n\nexport default Button\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_NAME_ACTIVE", "CLASS_NAME_BUTTON", "CLASS_NAME_FOCUS", "SELECTOR_DATA_TOGGLE_CARROT", "SELECTOR_DATA_TOGGLES", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_TOGGLES_BUTTONS", "SELECTOR_INPUT", "SELECTOR_ACTIVE", "SELECTOR_BUTTON", "EVENT_CLICK_DATA_API", "EVENT_FOCUS_BLUR_DATA_API", "EVENT_LOAD_DATA_API", "<PERSON><PERSON>", "element", "_element", "shouldAvoidTriggerChange", "toggle", "triggerChangeEvent", "addAriaPressed", "rootElement", "closest", "input", "querySelector", "type", "checked", "classList", "contains", "activeElement", "removeClass", "trigger", "focus", "hasAttribute", "setAttribute", "toggleClass", "dispose", "removeData", "_jQueryInterface", "config", "avoidTriggerChange", "each", "$element", "data", "document", "on", "event", "button", "target", "initialButton", "hasClass", "preventDefault", "inputBtn", "tagName", "call", "test", "window", "buttons", "slice", "querySelectorAll", "i", "len", "length", "add", "remove", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;EASA;;;;;;EAMA,IAAMA,IAAI,GAAG,QAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,WAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,iBAAiB,GAAG,QAA1B;EACA,IAAMC,iBAAiB,GAAG,KAA1B;EACA,IAAMC,gBAAgB,GAAG,OAAzB;EAEA,IAAMC,2BAA2B,GAAG,yBAApC;EACA,IAAMC,qBAAqB,GAAG,yBAA9B;EACA,IAAMC,oBAAoB,GAAG,wBAA7B;EACA,IAAMC,6BAA6B,GAAG,8BAAtC;EACA,IAAMC,cAAc,GAAG,4BAAvB;EACA,IAAMC,eAAe,GAAG,SAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EAEA,IAAMC,oBAAoB,aAAWf,SAAX,GAAuBC,YAAjD;EACA,IAAMe,yBAAyB,GAAG,UAAQhB,SAAR,GAAoBC,YAApB,mBACDD,SADC,GACWC,YADX,CAAlC;EAEA,IAAMgB,mBAAmB,YAAUjB,SAAV,GAAsBC,YAA/C;EAEA;;;;;;MAMMiB;EACJ,kBAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACA,SAAKE,wBAAL,GAAgC,KAAhC;EACD;;;;;EAQD;WAEAC,SAAA,kBAAS;EACP,QAAIC,kBAAkB,GAAG,IAAzB;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAMC,WAAW,GAAGtB,qBAAC,CAAC,KAAKiB,QAAN,CAAD,CAAiBM,OAAjB,CAAyBjB,qBAAzB,EAAgD,CAAhD,CAApB;;EAEA,QAAIgB,WAAJ,EAAiB;EACf,UAAME,KAAK,GAAG,KAAKP,QAAL,CAAcQ,aAAd,CAA4BhB,cAA5B,CAAd;;EAEA,UAAIe,KAAJ,EAAW;EACT,YAAIA,KAAK,CAACE,IAAN,KAAe,OAAnB,EAA4B;EAC1B,cAAIF,KAAK,CAACG,OAAN,IAAiB,KAAKV,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC3B,iBAAjC,CAArB,EAA0E;EACxEkB,YAAAA,kBAAkB,GAAG,KAArB;EACD,WAFD,MAEO;EACL,gBAAMU,aAAa,GAAGR,WAAW,CAACG,aAAZ,CAA0Bf,eAA1B,CAAtB;;EAEA,gBAAIoB,aAAJ,EAAmB;EACjB9B,cAAAA,qBAAC,CAAC8B,aAAD,CAAD,CAAiBC,WAAjB,CAA6B7B,iBAA7B;EACD;EACF;EACF;;EAED,YAAIkB,kBAAJ,EAAwB;EACtB;EACA,cAAII,KAAK,CAACE,IAAN,KAAe,UAAf,IAA6BF,KAAK,CAACE,IAAN,KAAe,OAAhD,EAAyD;EACvDF,YAAAA,KAAK,CAACG,OAAN,GAAgB,CAAC,KAAKV,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC3B,iBAAjC,CAAjB;EACD;;EAED,cAAI,CAAC,KAAKgB,wBAAV,EAAoC;EAClClB,YAAAA,qBAAC,CAACwB,KAAD,CAAD,CAASQ,OAAT,CAAiB,QAAjB;EACD;EACF;;EAEDR,QAAAA,KAAK,CAACS,KAAN;EACAZ,QAAAA,cAAc,GAAG,KAAjB;EACD;EACF;;EAED,QAAI,EAAE,KAAKJ,QAAL,CAAciB,YAAd,CAA2B,UAA3B,KAA0C,KAAKjB,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC,UAAjC,CAA5C,CAAJ,EAA+F;EAC7F,UAAIR,cAAJ,EAAoB;EAClB,aAAKJ,QAAL,CAAckB,YAAd,CAA2B,cAA3B,EAA2C,CAAC,KAAKlB,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC3B,iBAAjC,CAA5C;EACD;;EAED,UAAIkB,kBAAJ,EAAwB;EACtBpB,QAAAA,qBAAC,CAAC,KAAKiB,QAAN,CAAD,CAAiBmB,WAAjB,CAA6BlC,iBAA7B;EACD;EACF;EACF;;WAEDmC,UAAA,mBAAU;EACRrC,IAAAA,qBAAC,CAACsC,UAAF,CAAa,KAAKrB,QAAlB,EAA4BrB,QAA5B;EACA,SAAKqB,QAAL,GAAgB,IAAhB;EACD;;;WAIMsB,mBAAP,0BAAwBC,MAAxB,EAAgCC,kBAAhC,EAAoD;EAClD,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAMC,QAAQ,GAAG3C,qBAAC,CAAC,IAAD,CAAlB;EACA,UAAI4C,IAAI,GAAGD,QAAQ,CAACC,IAAT,CAAchD,QAAd,CAAX;;EAEA,UAAI,CAACgD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI7B,MAAJ,CAAW,IAAX,CAAP;EACA4B,QAAAA,QAAQ,CAACC,IAAT,CAAchD,QAAd,EAAwBgD,IAAxB;EACD;;EAEDA,MAAAA,IAAI,CAAC1B,wBAAL,GAAgCuB,kBAAhC;;EAEA,UAAID,MAAM,KAAK,QAAf,EAAyB;EACvBI,QAAAA,IAAI,CAACJ,MAAD,CAAJ;EACD;EACF,KAdM,CAAP;EAeD;;;;0BA7EoB;EACnB,aAAO7C,OAAP;EACD;;;;;EA8EH;;;;;;;AAMAK,uBAAC,CAAC6C,QAAD,CAAD,CACGC,EADH,CACMlC,oBADN,EAC4BP,2BAD5B,EACyD,UAAA0C,KAAK,EAAI;EAC9D,MAAIC,MAAM,GAAGD,KAAK,CAACE,MAAnB;EACA,MAAMC,aAAa,GAAGF,MAAtB;;EAEA,MAAI,CAAChD,qBAAC,CAACgD,MAAD,CAAD,CAAUG,QAAV,CAAmBhD,iBAAnB,CAAL,EAA4C;EAC1C6C,IAAAA,MAAM,GAAGhD,qBAAC,CAACgD,MAAD,CAAD,CAAUzB,OAAV,CAAkBZ,eAAlB,EAAmC,CAAnC,CAAT;EACD;;EAED,MAAI,CAACqC,MAAD,IAAWA,MAAM,CAACd,YAAP,CAAoB,UAApB,CAAX,IAA8Cc,MAAM,CAACpB,SAAP,CAAiBC,QAAjB,CAA0B,UAA1B,CAAlD,EAAyF;EACvFkB,IAAAA,KAAK,CAACK,cAAN,GADuF;EAExF,GAFD,MAEO;EACL,QAAMC,QAAQ,GAAGL,MAAM,CAACvB,aAAP,CAAqBhB,cAArB,CAAjB;;EAEA,QAAI4C,QAAQ,KAAKA,QAAQ,CAACnB,YAAT,CAAsB,UAAtB,KAAqCmB,QAAQ,CAACzB,SAAT,CAAmBC,QAAnB,CAA4B,UAA5B,CAA1C,CAAZ,EAAgG;EAC9FkB,MAAAA,KAAK,CAACK,cAAN,GAD8F;;EAE9F;EACD;;EAED,QAAIF,aAAa,CAACI,OAAd,KAA0B,OAA1B,IAAqCN,MAAM,CAACM,OAAP,KAAmB,OAA5D,EAAqE;EACnEvC,MAAAA,MAAM,CAACwB,gBAAP,CAAwBgB,IAAxB,CAA6BvD,qBAAC,CAACgD,MAAD,CAA9B,EAAwC,QAAxC,EAAkDE,aAAa,CAACI,OAAd,KAA0B,OAA5E;EACD;EACF;EACF,CAvBH,EAwBGR,EAxBH,CAwBMjC,yBAxBN,EAwBiCR,2BAxBjC,EAwB8D,UAAA0C,KAAK,EAAI;EACnE,MAAMC,MAAM,GAAGhD,qBAAC,CAAC+C,KAAK,CAACE,MAAP,CAAD,CAAgB1B,OAAhB,CAAwBZ,eAAxB,EAAyC,CAAzC,CAAf;EACAX,EAAAA,qBAAC,CAACgD,MAAD,CAAD,CAAUZ,WAAV,CAAsBhC,gBAAtB,EAAwC,eAAeoD,IAAf,CAAoBT,KAAK,CAACrB,IAA1B,CAAxC;EACD,CA3BH;AA6BA1B,uBAAC,CAACyD,MAAD,CAAD,CAAUX,EAAV,CAAahC,mBAAb,EAAkC,YAAM;EACtC;EAEA;EACA,MAAI4C,OAAO,GAAG,GAAGC,KAAH,CAASJ,IAAT,CAAcV,QAAQ,CAACe,gBAAT,CAA0BpD,6BAA1B,CAAd,CAAd;;EACA,OAAK,IAAIqD,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,OAAO,CAACK,MAA9B,EAAsCF,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,QAAMb,MAAM,GAAGU,OAAO,CAACG,CAAD,CAAtB;EACA,QAAMrC,KAAK,GAAGwB,MAAM,CAACvB,aAAP,CAAqBhB,cAArB,CAAd;;EACA,QAAIe,KAAK,CAACG,OAAN,IAAiBH,KAAK,CAACU,YAAN,CAAmB,SAAnB,CAArB,EAAoD;EAClDc,MAAAA,MAAM,CAACpB,SAAP,CAAiBoC,GAAjB,CAAqB9D,iBAArB;EACD,KAFD,MAEO;EACL8C,MAAAA,MAAM,CAACpB,SAAP,CAAiBqC,MAAjB,CAAwB/D,iBAAxB;EACD;EACF,GAbqC;;;EAgBtCwD,EAAAA,OAAO,GAAG,GAAGC,KAAH,CAASJ,IAAT,CAAcV,QAAQ,CAACe,gBAAT,CAA0BrD,oBAA1B,CAAd,CAAV;;EACA,OAAK,IAAIsD,EAAC,GAAG,CAAR,EAAWC,IAAG,GAAGJ,OAAO,CAACK,MAA9B,EAAsCF,EAAC,GAAGC,IAA1C,EAA+CD,EAAC,EAAhD,EAAoD;EAClD,QAAMb,OAAM,GAAGU,OAAO,CAACG,EAAD,CAAtB;;EACA,QAAIb,OAAM,CAACkB,YAAP,CAAoB,cAApB,MAAwC,MAA5C,EAAoD;EAClDlB,MAAAA,OAAM,CAACpB,SAAP,CAAiBoC,GAAjB,CAAqB9D,iBAArB;EACD,KAFD,MAEO;EACL8C,MAAAA,OAAM,CAACpB,SAAP,CAAiBqC,MAAjB,CAAwB/D,iBAAxB;EACD;EACF;EACF,CAzBD;EA2BA;;;;;;AAMAF,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaqB,MAAM,CAACwB,gBAApB;AACAvC,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWyE,WAAX,GAAyBpD,MAAzB;;AACAf,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAW0E,UAAX,GAAwB,YAAM;EAC5BpE,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOgB,MAAM,CAACwB,gBAAd;EACD,CAHD;;;;;;;;"}