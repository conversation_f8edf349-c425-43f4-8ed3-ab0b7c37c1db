// main: ../main.scss
/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 20px 0;
  position: relative;

  h1 {
    font-size: 24px;
    font-weight: 300;
    margin: 0 0 5px 0;
  }

  .breadcrumbs {
    ol {
      display: flex;
      flex-wrap: wrap;
      list-style: none;
      padding: 0;
      margin: 0;
      font-size: 14px;
      font-weight: 400;

      li+li {
        padding-left: 10px;
      }

      li+li::before {
        content: "/";
        display: inline-block;
        padding-right: 10px;
        color: color-mix(in srgb, var(--default-color), transparent 70%);
      }
    }
  }
}