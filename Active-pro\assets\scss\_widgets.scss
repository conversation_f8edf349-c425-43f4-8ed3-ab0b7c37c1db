/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widgets-container {
  margin: 60px 0 30px 0;
}

.widget-title {
  color: var(--heading-color);
  font-size: 20px;
  font-weight: 600;
  padding: 0 0 0 10px;
  margin: 0 0 20px 0;
  border-left: 4px solid var(--accent-color);
}

.widget-item {
  margin-bottom: 30px;
  background-color: color-mix(in srgb, var(--default-color), transparent 98%);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  padding: 30px;
  border-radius: 5px;

  &:last-child {
    margin-bottom: 0;
  }
}

@import './widgets/_blog-author.scss';
@import './widgets/_search.scss';
@import './widgets/_recent-posts.scss';
@import './widgets/_tags.scss';