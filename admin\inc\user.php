  <?php
// database connection info
include'../function.php';

// find out how many rows are in the table
$sql = "SELECT COUNT(*) FROM user";
$result = mysql_query($sql) or trigger_error("SQL", E_USER_ERROR);
$r = mysql_fetch_row($result);
$numrows = $r[0];

// number of rows to show per page
$rowsperpage = 20;
// find out total pages
$totalpages = ceil($numrows / $rowsperpage);

// get the current page or set a default
if (isset($_GET['currentpage']) && is_numeric($_GET['currentpage'])) {
   // cast var as int
   $currentpage = (int) $_GET['currentpage'];
} else {
   // default page num
   $currentpage = 1;
} // end if

// if current page is greater than total pages...
if ($currentpage > $totalpages) {
   // set current page to last page
   $currentpage = $totalpages;
} // end if
// if current page is less than first page...
if ($currentpage < 1) {
   // set current page to first page
   $currentpage = 1;
} // end if

// the offset of the list, based on current page
$offset = ($currentpage - 1) * $rowsperpage;

//count the amount of clients

// get the info from the db
$sql = "SELECT * FROM user LIMIT $offset, $rowsperpage";
$result = mysql_query($sql) or trigger_error("SQL", E_USER_ERROR);

echo" Number of clients $numrows
<table class='reference' width='100%'>
<tbody>
<tr>
<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Image</td>
<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Name</td>
<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Viewed</td>
<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Status</td>
<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Contact</td>
<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Date Posted</td>

<td align='center'  valign='top' width='10' height='12' style='border:1px #79E1E1 solid; background-color:#C0F1F1;'>Email Address</td>

</tr>";

while($row = mysql_fetch_assoc($result))
{
  $id     = $row['id'];
  $images = $row['image_name'];
  $status = $row['activated'];
  $stat   = $row['activated'];
  $visible  = $row['visible'];
  $gender   = $row['gender'];
  $email    = $row['username'];

//Infromation About Client That will be Showed
echo"<td align='center' valign='top' width='10' height='22' style='border:1px #AFEEEE solid;'> <img src='$images' width='50' height='50'></td>";
echo"<td align='center' valign='top' width='10' height='22' style='border:1px #AFEEEE solid;'>".$row['firstname']." ".$row['lastname']."</a></td>";
echo"<td align='center' valign='top' width='10' height='22' style='border:1px #AFEEEE solid;'>".$row['views']."</td>";
echo"<td align='center' valign='top' width='10' height='22' style='border:1px #AFEEEE solid;'>";
if(($status=='1')&&($gender=='Male'))
 {
 echo"<img src='images/m-active.png' border='0'>";
 }
if(($status=='0')&&($gender=='Male'))
 {
 echo"<img src='images/m-inactive.png' border='0'>";
 }

if(($status=='1')&&($gender=='Female'))
 {
 echo"<img src='images/f-active.png' border='0'>";
 }
if(($status=='0')&&($gender=='Female'))
 {
 echo"<img src='images/f-inactive.png' border='0'>";
 }
echo"</td>";
echo"<td align='center' valign='top' width='10' height='22' style='border:1px #AFEEEE solid;'>".$row['phone']."</td>";
echo"<td align='center' valign='top' width='10' height='22' style='border:1px #AFEEEE solid;'>".$row['date']."</td>";
echo"<td align='center' valign='top' width='10' height='30' style='border:1px #AFEEEE solid;'><a href='mailto:$email'><img src='images/mail-icon.png' border='0'></a></td>";

echo "
 </tbody>
</tr>";
  }
     echo"</table>";


// end while

/******  build the pagination links ******/
// range of num links to show
$range = 3;

// if not on page 1, don't show back links
if ($currentpage > 1) {
   // show << link to go back to page 1
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=client&currentpage=1'>First</a> ";
   // get previous page num
   $prevpage = $currentpage - 1;
   // show < link to go back to 1 page
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=client&currentpage=$prevpage'>Prev</a> ";
} // end if

// loop to show links to range of pages around current page
for ($x = ($currentpage - $range); $x < (($currentpage + $range) + 1); $x++) {
   // if it's a valid page number...
   if (($x > 0) && ($x <= $totalpages)) {
      // if we're on current page...
      if ($x == $currentpage) {
         // 'highlight' it but don't make a link
         echo " [<b>$x</b>] ";
      // if not current page...
      } else {
         // make it a link
         echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=client&currentpage=$x'>$x</a> ";
      } // end else
   } // end if
} // end for

// if not on last page, show forward and last page links
if ($currentpage != $totalpages) {
   // get next page
   $nextpage = $currentpage + 1;
    // echo forward link for next page
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=client&currentpage=$nextpage'>Next</a> ";
   // echo forward link for lastpage
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=client&currentpage=$totalpages'>Last</a> ";
} // end if
/****** end build pagination links ******/


?>
