/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 100px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;

  h2 {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 0;
  }

  p {
    font-size: 15px;
    margin-bottom: 10px;
  }
}

@import './sections/_about.scss';
@import './sections/_about-2.scss';
@import './sections/_services.scss';
@import './sections/_stats.scss';
@import './sections/_blog-posts.scss';
@import './sections/_tabs.scss';
@import './sections/_services-2.scss';
@import './sections/_pricing.scss';
@import './sections/_faq.scss';
@import './sections/_testimonials.scss';
@import './sections/_portfolio.scss';
@import './sections/_portfolio-details.scss';
@import './sections/_team.scss';
@import './sections/_blog-posts-2.scss';
@import './sections/_blog-pagination.scss';
@import './sections/_blog-details.scss';
@import './sections/_blog-comments.scss';
@import './sections/_comment-form.scss';
@import './sections/_contact.scss';