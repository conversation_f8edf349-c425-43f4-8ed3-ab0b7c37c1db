{"version": 3, "file": "modal.js", "sources": ["../src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLABLE = 'modal-dialog-scrollable'\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = element.querySelector(SELECTOR_DIALOG)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if ($(this._element).hasClass(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = $.Event(EVENT_SHOW, {\n      relatedTarget\n    })\n\n    $(this._element).trigger(showEvent)\n\n    if (this._isShown || showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(this._element).on(\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    $(this._dialog).on(EVENT_MOUSEDOWN_DISMISS, () => {\n      $(this._element).one(EVENT_MOUSEUP_DISMISS, event => {\n        if ($(event.target).is(this._element)) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n\n    if (!this._isShown || hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = false\n    const transition = $(this._element).hasClass(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(document).off(EVENT_FOCUSIN)\n\n    $(this._element).removeClass(CLASS_NAME_SHOW)\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n    $(this._dialog).off(EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, event => this._hideModal(event))\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => $(htmlElement).off(EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    $(document).off(EVENT_FOCUSIN)\n\n    $.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEventPrevented = $.Event(EVENT_HIDE_PREVENTED)\n\n      $(this._element).trigger(hideEventPrevented)\n      if (hideEventPrevented.isDefaultPrevented()) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n\n      const modalTransitionDuration = Util.getTransitionDurationFromElement(this._dialog)\n      $(this._element).off(Util.TRANSITION_END)\n\n      $(this._element).one(Util.TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          $(this._element).one(Util.TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n            .emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n        .emulateTransitionEnd(modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  _showElement(relatedTarget) {\n    const transition = $(this._element).hasClass(CLASS_NAME_FADE)\n    const modalBody = this._dialog ? this._dialog.querySelector(SELECTOR_MODAL_BODY) : null\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n\n    if ($(this._dialog).hasClass(CLASS_NAME_SCROLLABLE) && modalBody) {\n      modalBody.scrollTop = 0\n    } else {\n      this._element.scrollTop = 0\n    }\n\n    if (transition) {\n      Util.reflow(this._element)\n    }\n\n    $(this._element).addClass(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const shownEvent = $.Event(EVENT_SHOWN, {\n      relatedTarget\n    })\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (transition) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._dialog)\n\n      $(this._dialog)\n        .one(Util.TRANSITION_END, transitionComplete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    $(document)\n      .off(EVENT_FOCUSIN) // Guard against infinite focus loop\n      .on(EVENT_FOCUSIN, event => {\n        if (document !== event.target &&\n            this._element !== event.target &&\n            $(this._element).has(event.target).length === 0) {\n          this._element.focus()\n        }\n      })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      $(this._element).on(EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.which === ESCAPE_KEYCODE) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.which === ESCAPE_KEYCODE) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else if (!this._isShown) {\n      $(this._element).off(EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      $(window).on(EVENT_RESIZE, event => this.handleUpdate(event))\n    } else {\n      $(window).off(EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      $(document.body).removeClass(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      $(this._element).trigger(EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    if (this._backdrop) {\n      $(this._backdrop).remove()\n      this._backdrop = null\n    }\n  }\n\n  _showBackdrop(callback) {\n    const animate = $(this._element).hasClass(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE : ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      $(this._backdrop).appendTo(document.body)\n\n      $(this._element).on(EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        Util.reflow(this._backdrop)\n      }\n\n      $(this._backdrop).addClass(CLASS_NAME_SHOW)\n\n      if (!callback) {\n        return\n      }\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n      $(this._backdrop)\n        .one(Util.TRANSITION_END, callback)\n        .emulateTransitionEnd(backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      $(this._backdrop).removeClass(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        if (callback) {\n          callback()\n        }\n      }\n\n      if ($(this._element).hasClass(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callbackRemove)\n          .emulateTransitionEnd(backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else if (callback) {\n      callback()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // todo (fat): these should probably be refactored out of modal.js\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n      const fixedContent = [].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT))\n      const stickyContent = [].slice.call(document.querySelectorAll(SELECTOR_STICKY_CONTENT))\n\n      // Adjust fixed content padding\n      $(fixedContent).each((index, element) => {\n        const actualPadding = element.style.paddingRight\n        const calculatedPadding = $(element).css('padding-right')\n        $(element)\n          .data('padding-right', actualPadding)\n          .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      })\n\n      // Adjust sticky content margin\n      $(stickyContent).each((index, element) => {\n        const actualMargin = element.style.marginRight\n        const calculatedMargin = $(element).css('margin-right')\n        $(element)\n          .data('margin-right', actualMargin)\n          .css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n      })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = $(document.body).css('padding-right')\n      $(document.body)\n        .data('padding-right', actualPadding)\n        .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n    }\n\n    $(document.body).addClass(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    const fixedContent = [].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT))\n    $(fixedContent).each((index, element) => {\n      const padding = $(element).data('padding-right')\n      $(element).removeData('padding-right')\n      element.style.paddingRight = padding ? padding : ''\n    })\n\n    // Restore sticky content\n    const elements = [].slice.call(document.querySelectorAll(`${SELECTOR_STICKY_CONTENT}`))\n    $(elements).each((index, element) => {\n      const margin = $(element).data('margin-right')\n      if (typeof margin !== 'undefined') {\n        $(element).css('margin-right', margin).removeData('margin-right')\n      }\n    })\n\n    // Restore body padding\n    const padding = $(document.body).data('padding-right')\n    $(document.body).removeData('padding-right')\n    document.body.style.paddingRight = padding ? padding : ''\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static _jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$(this).data(),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  let target\n  const selector = Util.getSelectorFromElement(this)\n\n  if (selector) {\n    target = document.querySelector(selector)\n  }\n\n  const config = $(target).data(DATA_KEY) ?\n    'toggle' : {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  const $target = $(target).one(EVENT_SHOW, showEvent => {\n    if (showEvent.isDefaultPrevented()) {\n      // Only register focus restorer if modal will actually get shown\n      return\n    }\n\n    $target.one(EVENT_HIDDEN, () => {\n      if ($(this).is(':visible')) {\n        this.focus()\n      }\n    })\n  })\n\n  Modal._jQueryInterface.call($(target), config, this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Modal._jQueryInterface\n$.fn[NAME].Constructor = Modal\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Modal._jQueryInterface\n}\n\nexport default Modal\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "focus", "show", "DefaultType", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_CLICK_DATA_API", "CLASS_NAME_SCROLLABLE", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "element", "config", "_config", "_getConfig", "_element", "_dialog", "querySelector", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_isTransitioning", "_scrollbarWidth", "toggle", "relatedTarget", "hide", "hasClass", "showEvent", "Event", "trigger", "isDefaultPrevented", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "on", "event", "one", "target", "is", "_showBackdrop", "_showElement", "preventDefault", "hideEvent", "transition", "document", "off", "removeClass", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "TRANSITION_END", "_hideModal", "emulateTransitionEnd", "dispose", "window", "for<PERSON>ach", "htmlElement", "removeData", "handleUpdate", "typeCheckConfig", "_triggerBackdropTransition", "hideEventPrevented", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "style", "overflowY", "classList", "add", "modalTransitionDuration", "remove", "modalBody", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "body", "append<PERSON><PERSON><PERSON>", "display", "removeAttribute", "setAttribute", "scrollTop", "reflow", "addClass", "_enforceFocus", "shownEvent", "transitionComplete", "has", "length", "which", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "createElement", "className", "appendTo", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "rect", "getBoundingClientRect", "Math", "round", "left", "right", "innerWidth", "_getScrollbarWidth", "fixedContent", "slice", "call", "querySelectorAll", "sticky<PERSON>ontent", "each", "index", "actualPadding", "calculatedPadding", "css", "data", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "elements", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "_jQueryInterface", "TypeError", "selector", "getSelectorFromElement", "tagName", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAG,OAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,UAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EACA,IAAMQ,cAAc,GAAG,EAAvB;;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,IAHO;EAIdC,EAAAA,IAAI,EAAE;EAJQ,CAAhB;EAOA,IAAMC,WAAW,GAAG;EAClBJ,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,SAHW;EAIlBC,EAAAA,IAAI,EAAE;EAJY,CAApB;EAOA,IAAME,UAAU,YAAUZ,SAA1B;EACA,IAAMa,oBAAoB,qBAAmBb,SAA7C;EACA,IAAMc,YAAY,cAAYd,SAA9B;EACA,IAAMe,UAAU,YAAUf,SAA1B;EACA,IAAMgB,WAAW,aAAWhB,SAA5B;EACA,IAAMiB,aAAa,eAAajB,SAAhC;EACA,IAAMkB,YAAY,cAAYlB,SAA9B;EACA,IAAMmB,mBAAmB,qBAAmBnB,SAA5C;EACA,IAAMoB,qBAAqB,uBAAqBpB,SAAhD;EACA,IAAMqB,qBAAqB,uBAAqBrB,SAAhD;EACA,IAAMsB,uBAAuB,yBAAuBtB,SAApD;EACA,IAAMuB,oBAAoB,aAAWvB,SAAX,GAAuBC,YAAjD;EAEA,IAAMuB,qBAAqB,GAAG,yBAA9B;EACA,IAAMC,6BAA6B,GAAG,yBAAtC;EACA,IAAMC,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,iBAAiB,GAAG,cAA1B;EAEA,IAAMC,eAAe,GAAG,eAAxB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMC,oBAAoB,GAAG,uBAA7B;EACA,IAAMC,qBAAqB,GAAG,wBAA9B;EACA,IAAMC,sBAAsB,GAAG,mDAA/B;EACA,IAAMC,uBAAuB,GAAG,aAAhC;EAEA;;;;;;MAMMC;EACJ,iBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBF,MAAhB,CAAf;EACA,SAAKG,QAAL,GAAgBJ,OAAhB;EACA,SAAKK,OAAL,GAAeL,OAAO,CAACM,aAAR,CAAsBb,eAAtB,CAAf;EACA,SAAKc,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,kBAAL,GAA0B,KAA1B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,gBAAL,GAAwB,KAAxB;EACA,SAAKC,eAAL,GAAuB,CAAvB;EACD;;;;;EAYD;WAEAC,SAAA,gBAAOC,aAAP,EAAsB;EACpB,WAAO,KAAKN,QAAL,GAAgB,KAAKO,IAAL,EAAhB,GAA8B,KAAK3C,IAAL,CAAU0C,aAAV,CAArC;EACD;;WAED1C,OAAA,cAAK0C,aAAL,EAAoB;EAAA;;EAClB,QAAI,KAAKN,QAAL,IAAiB,KAAKG,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI9C,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B1B,eAA1B,CAAJ,EAAgD;EAC9C,WAAKqB,gBAAL,GAAwB,IAAxB;EACD;;EAED,QAAMM,SAAS,GAAGpD,qBAAC,CAACqD,KAAF,CAAQzC,UAAR,EAAoB;EACpCqC,MAAAA,aAAa,EAAbA;EADoC,KAApB,CAAlB;EAIAjD,IAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBe,OAAjB,CAAyBF,SAAzB;;EAEA,QAAI,KAAKT,QAAL,IAAiBS,SAAS,CAACG,kBAAV,EAArB,EAAqD;EACnD;EACD;;EAED,SAAKZ,QAAL,GAAgB,IAAhB;;EAEA,SAAKa,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA5D,IAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBsB,EAAjB,CACE7C,mBADF,EAEEe,qBAFF,EAGE,UAAA+B,KAAK;EAAA,aAAI,KAAI,CAACZ,IAAL,CAAUY,KAAV,CAAJ;EAAA,KAHP;EAMA9D,IAAAA,qBAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgBqB,EAAhB,CAAmB1C,uBAAnB,EAA4C,YAAM;EAChDnB,MAAAA,qBAAC,CAAC,KAAI,CAACuC,QAAN,CAAD,CAAiBwB,GAAjB,CAAqB7C,qBAArB,EAA4C,UAAA4C,KAAK,EAAI;EACnD,YAAI9D,qBAAC,CAAC8D,KAAK,CAACE,MAAP,CAAD,CAAgBC,EAAhB,CAAmB,KAAI,CAAC1B,QAAxB,CAAJ,EAAuC;EACrC,UAAA,KAAI,CAACM,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKqB,aAAL,CAAmB;EAAA,aAAM,KAAI,CAACC,YAAL,CAAkBlB,aAAlB,CAAN;EAAA,KAAnB;EACD;;WAEDC,OAAA,cAAKY,KAAL,EAAY;EAAA;;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAACM,cAAN;EACD;;EAED,QAAI,CAAC,KAAKzB,QAAN,IAAkB,KAAKG,gBAA3B,EAA6C;EAC3C;EACD;;EAED,QAAMuB,SAAS,GAAGrE,qBAAC,CAACqD,KAAF,CAAQ5C,UAAR,CAAlB;EAEAT,IAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBe,OAAjB,CAAyBe,SAAzB;;EAEA,QAAI,CAAC,KAAK1B,QAAN,IAAkB0B,SAAS,CAACd,kBAAV,EAAtB,EAAsD;EACpD;EACD;;EAED,SAAKZ,QAAL,GAAgB,KAAhB;EACA,QAAM2B,UAAU,GAAGtE,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B1B,eAA1B,CAAnB;;EAEA,QAAI6C,UAAJ,EAAgB;EACd,WAAKxB,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKa,eAAL;;EACA,SAAKC,eAAL;;EAEA5D,IAAAA,qBAAC,CAACuE,QAAD,CAAD,CAAYC,GAAZ,CAAgB1D,aAAhB;EAEAd,IAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBkC,WAAjB,CAA6B/C,eAA7B;EAEA1B,IAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBiC,GAAjB,CAAqBxD,mBAArB;EACAhB,IAAAA,qBAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgBgC,GAAhB,CAAoBrD,uBAApB;;EAEA,QAAImD,UAAJ,EAAgB;EACd,UAAMI,kBAAkB,GAAGC,wBAAI,CAACC,gCAAL,CAAsC,KAAKrC,QAA3C,CAA3B;EAEAvC,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CACGwB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4B,UAAAf,KAAK;EAAA,eAAI,MAAI,CAACgB,UAAL,CAAgBhB,KAAhB,CAAJ;EAAA,OADjC,EAEGiB,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACL,WAAKI,UAAL;EACD;EACF;;WAEDE,UAAA,mBAAU;EACR,KAACC,MAAD,EAAS,KAAK1C,QAAd,EAAwB,KAAKC,OAA7B,EACG0C,OADH,CACW,UAAAC,WAAW;EAAA,aAAInF,qBAAC,CAACmF,WAAD,CAAD,CAAeX,GAAf,CAAmB3E,SAAnB,CAAJ;EAAA,KADtB;EAGA;;;;;;EAKAG,IAAAA,qBAAC,CAACuE,QAAD,CAAD,CAAYC,GAAZ,CAAgB1D,aAAhB;EAEAd,IAAAA,qBAAC,CAACoF,UAAF,CAAa,KAAK7C,QAAlB,EAA4B3C,QAA5B;EAEA,SAAKyC,OAAL,GAAe,IAAf;EACA,SAAKE,QAAL,GAAgB,IAAhB;EACA,SAAKC,OAAL,GAAe,IAAf;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,kBAAL,GAA0B,IAA1B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAKC,gBAAL,GAAwB,IAAxB;EACA,SAAKC,eAAL,GAAuB,IAAvB;EACD;;WAEDsC,eAAA,wBAAe;EACb,SAAK3B,aAAL;EACD;;;WAIDpB,aAAA,oBAAWF,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDjC,OADC,EAEDiC,MAFC,CAAN;EAIAuC,IAAAA,wBAAI,CAACW,eAAL,CAAqB5F,IAArB,EAA2B0C,MAA3B,EAAmC5B,WAAnC;EACA,WAAO4B,MAAP;EACD;;WAEDmD,6BAAA,sCAA6B;EAAA;;EAC3B,QAAI,KAAKlD,OAAL,CAAajC,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAMoF,kBAAkB,GAAGxF,qBAAC,CAACqD,KAAF,CAAQ3C,oBAAR,CAA3B;EAEAV,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBe,OAAjB,CAAyBkC,kBAAzB;;EACA,UAAIA,kBAAkB,CAACjC,kBAAnB,EAAJ,EAA6C;EAC3C;EACD;;EAED,UAAMkC,kBAAkB,GAAG,KAAKlD,QAAL,CAAcmD,YAAd,GAA6BnB,QAAQ,CAACoB,eAAT,CAAyBC,YAAjF;;EAEA,UAAI,CAACH,kBAAL,EAAyB;EACvB,aAAKlD,QAAL,CAAcsD,KAAd,CAAoBC,SAApB,GAAgC,QAAhC;EACD;;EAED,WAAKvD,QAAL,CAAcwD,SAAd,CAAwBC,GAAxB,CAA4BrE,iBAA5B;;EAEA,UAAMsE,uBAAuB,GAAGtB,wBAAI,CAACC,gCAAL,CAAsC,KAAKpC,OAA3C,CAAhC;EACAxC,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBiC,GAAjB,CAAqBG,wBAAI,CAACE,cAA1B;EAEA7E,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBwB,GAAjB,CAAqBY,wBAAI,CAACE,cAA1B,EAA0C,YAAM;EAC9C,QAAA,MAAI,CAACtC,QAAL,CAAcwD,SAAd,CAAwBG,MAAxB,CAA+BvE,iBAA/B;;EACA,YAAI,CAAC8D,kBAAL,EAAyB;EACvBzF,UAAAA,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBwB,GAAjB,CAAqBY,wBAAI,CAACE,cAA1B,EAA0C,YAAM;EAC9C,YAAA,MAAI,CAACtC,QAAL,CAAcsD,KAAd,CAAoBC,SAApB,GAAgC,EAAhC;EACD,WAFD,EAGGf,oBAHH,CAGwB,MAAI,CAACxC,QAH7B,EAGuC0D,uBAHvC;EAID;EACF,OARD,EASGlB,oBATH,CASwBkB,uBATxB;;EAUA,WAAK1D,QAAL,CAAcjC,KAAd;EACD,KA9BD,MA8BO;EACL,WAAK4C,IAAL;EACD;EACF;;WAEDiB,eAAA,sBAAalB,aAAb,EAA4B;EAAA;;EAC1B,QAAMqB,UAAU,GAAGtE,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B1B,eAA1B,CAAnB;EACA,QAAM0E,SAAS,GAAG,KAAK3D,OAAL,GAAe,KAAKA,OAAL,CAAaC,aAAb,CAA2BZ,mBAA3B,CAAf,GAAiE,IAAnF;;EAEA,QAAI,CAAC,KAAKU,QAAL,CAAc6D,UAAf,IACA,KAAK7D,QAAL,CAAc6D,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACAhC,MAAAA,QAAQ,CAACiC,IAAT,CAAcC,WAAd,CAA0B,KAAKlE,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAcsD,KAAd,CAAoBa,OAApB,GAA8B,OAA9B;;EACA,SAAKnE,QAAL,CAAcoE,eAAd,CAA8B,aAA9B;;EACA,SAAKpE,QAAL,CAAcqE,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKrE,QAAL,CAAcqE,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EAEA,QAAI5G,qBAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgBW,QAAhB,CAAyB9B,qBAAzB,KAAmD8E,SAAvD,EAAkE;EAChEA,MAAAA,SAAS,CAACU,SAAV,GAAsB,CAAtB;EACD,KAFD,MAEO;EACL,WAAKtE,QAAL,CAAcsE,SAAd,GAA0B,CAA1B;EACD;;EAED,QAAIvC,UAAJ,EAAgB;EACdK,MAAAA,wBAAI,CAACmC,MAAL,CAAY,KAAKvE,QAAjB;EACD;;EAEDvC,IAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBwE,QAAjB,CAA0BrF,eAA1B;;EAEA,QAAI,KAAKW,OAAL,CAAa/B,KAAjB,EAAwB;EACtB,WAAK0G,aAAL;EACD;;EAED,QAAMC,UAAU,GAAGjH,qBAAC,CAACqD,KAAF,CAAQxC,WAAR,EAAqB;EACtCoC,MAAAA,aAAa,EAAbA;EADsC,KAArB,CAAnB;;EAIA,QAAMiE,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,UAAI,MAAI,CAAC7E,OAAL,CAAa/B,KAAjB,EAAwB;EACtB,QAAA,MAAI,CAACiC,QAAL,CAAcjC,KAAd;EACD;;EAED,MAAA,MAAI,CAACwC,gBAAL,GAAwB,KAAxB;EACA9C,MAAAA,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBe,OAAjB,CAAyB2D,UAAzB;EACD,KAPD;;EASA,QAAI3C,UAAJ,EAAgB;EACd,UAAMI,kBAAkB,GAAGC,wBAAI,CAACC,gCAAL,CAAsC,KAAKpC,OAA3C,CAA3B;EAEAxC,MAAAA,qBAAC,CAAC,KAAKwC,OAAN,CAAD,CACGuB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4BqC,kBAD5B,EAEGnC,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACLwC,MAAAA,kBAAkB;EACnB;EACF;;WAEDF,gBAAA,yBAAgB;EAAA;;EACdhH,IAAAA,qBAAC,CAACuE,QAAD,CAAD,CACGC,GADH,CACO1D,aADP;EAAA,KAEG+C,EAFH,CAEM/C,aAFN,EAEqB,UAAAgD,KAAK,EAAI;EAC1B,UAAIS,QAAQ,KAAKT,KAAK,CAACE,MAAnB,IACA,MAAI,CAACzB,QAAL,KAAkBuB,KAAK,CAACE,MADxB,IAEAhE,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiB4E,GAAjB,CAAqBrD,KAAK,CAACE,MAA3B,EAAmCoD,MAAnC,KAA8C,CAFlD,EAEqD;EACnD,QAAA,MAAI,CAAC7E,QAAL,CAAcjC,KAAd;EACD;EACF,KARH;EASD;;WAEDqD,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKhB,QAAT,EAAmB;EACjB3C,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBsB,EAAjB,CAAoB5C,qBAApB,EAA2C,UAAA6C,KAAK,EAAI;EAClD,YAAI,MAAI,CAACzB,OAAL,CAAahC,QAAb,IAAyByD,KAAK,CAACuD,KAAN,KAAgBnH,cAA7C,EAA6D;EAC3D4D,UAAAA,KAAK,CAACM,cAAN;;EACA,UAAA,MAAI,CAAClB,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAACb,OAAL,CAAahC,QAAd,IAA0ByD,KAAK,CAACuD,KAAN,KAAgBnH,cAA9C,EAA8D;EACnE,UAAA,MAAI,CAACqF,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO,IAAI,CAAC,KAAK5C,QAAV,EAAoB;EACzB3C,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBiC,GAAjB,CAAqBvD,qBAArB;EACD;EACF;;WAED2C,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKjB,QAAT,EAAmB;EACjB3C,MAAAA,qBAAC,CAACiF,MAAD,CAAD,CAAUpB,EAAV,CAAa9C,YAAb,EAA2B,UAAA+C,KAAK;EAAA,eAAI,MAAI,CAACuB,YAAL,CAAkBvB,KAAlB,CAAJ;EAAA,OAAhC;EACD,KAFD,MAEO;EACL9D,MAAAA,qBAAC,CAACiF,MAAD,CAAD,CAAUT,GAAV,CAAczD,YAAd;EACD;EACF;;WAED+D,aAAA,sBAAa;EAAA;;EACX,SAAKvC,QAAL,CAAcsD,KAAd,CAAoBa,OAApB,GAA8B,MAA9B;;EACA,SAAKnE,QAAL,CAAcqE,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAKrE,QAAL,CAAcoE,eAAd,CAA8B,YAA9B;;EACA,SAAKpE,QAAL,CAAcoE,eAAd,CAA8B,MAA9B;;EACA,SAAK7D,gBAAL,GAAwB,KAAxB;;EACA,SAAKoB,aAAL,CAAmB,YAAM;EACvBlE,MAAAA,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiB/B,WAAjB,CAA6BjD,eAA7B;;EACA,MAAA,MAAI,CAAC8F,iBAAL;;EACA,MAAA,MAAI,CAACC,eAAL;;EACAvH,MAAAA,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBe,OAAjB,CAAyB3C,YAAzB;EACD,KALD;EAMD;;WAED6G,kBAAA,2BAAkB;EAChB,QAAI,KAAK9E,SAAT,EAAoB;EAClB1C,MAAAA,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkBwD,MAAlB;EACA,WAAKxD,SAAL,GAAiB,IAAjB;EACD;EACF;;WAEDwB,gBAAA,uBAAcuD,QAAd,EAAwB;EAAA;;EACtB,QAAMC,OAAO,GAAG1H,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B1B,eAA1B,IACdA,eADc,GACI,EADpB;;EAGA,QAAI,KAAKkB,QAAL,IAAiB,KAAKN,OAAL,CAAajC,QAAlC,EAA4C;EAC1C,WAAKsC,SAAL,GAAiB6B,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKjF,SAAL,CAAekF,SAAf,GAA2BrG,mBAA3B;;EAEA,UAAImG,OAAJ,EAAa;EACX,aAAKhF,SAAL,CAAeqD,SAAf,CAAyBC,GAAzB,CAA6B0B,OAA7B;EACD;;EAED1H,MAAAA,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkBmF,QAAlB,CAA2BtD,QAAQ,CAACiC,IAApC;EAEAxG,MAAAA,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBsB,EAAjB,CAAoB7C,mBAApB,EAAyC,UAAA8C,KAAK,EAAI;EAChD,YAAI,MAAI,CAACjB,oBAAT,EAA+B;EAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,YAAIiB,KAAK,CAACE,MAAN,KAAiBF,KAAK,CAACgE,aAA3B,EAA0C;EACxC;EACD;;EAED,QAAA,MAAI,CAACvC,0BAAL;EACD,OAXD;;EAaA,UAAImC,OAAJ,EAAa;EACX/C,QAAAA,wBAAI,CAACmC,MAAL,CAAY,KAAKpE,SAAjB;EACD;;EAED1C,MAAAA,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkBqE,QAAlB,CAA2BrF,eAA3B;;EAEA,UAAI,CAAC+F,QAAL,EAAe;EACb;EACD;;EAED,UAAI,CAACC,OAAL,EAAc;EACZD,QAAAA,QAAQ;EACR;EACD;;EAED,UAAMM,0BAA0B,GAAGpD,wBAAI,CAACC,gCAAL,CAAsC,KAAKlC,SAA3C,CAAnC;EAEA1C,MAAAA,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CACGqB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4B4C,QAD5B,EAEG1C,oBAFH,CAEwBgD,0BAFxB;EAGD,KA3CD,MA2CO,IAAI,CAAC,KAAKpF,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C1C,MAAAA,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkB+B,WAAlB,CAA8B/C,eAA9B;;EAEA,UAAMsG,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACR,eAAL;;EACA,YAAIC,QAAJ,EAAc;EACZA,UAAAA,QAAQ;EACT;EACF,OALD;;EAOA,UAAIzH,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B1B,eAA1B,CAAJ,EAAgD;EAC9C,YAAMsG,2BAA0B,GAAGpD,wBAAI,CAACC,gCAAL,CAAsC,KAAKlC,SAA3C,CAAnC;;EAEA1C,QAAAA,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CACGqB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4BmD,cAD5B,EAEGjD,oBAFH,CAEwBgD,2BAFxB;EAGD,OAND,MAMO;EACLC,QAAAA,cAAc;EACf;EACF,KAnBM,MAmBA,IAAIP,QAAJ,EAAc;EACnBA,MAAAA,QAAQ;EACT;EACF;EAGD;EACA;EACA;;;WAEA/D,gBAAA,yBAAgB;EACd,QAAM+B,kBAAkB,GAAG,KAAKlD,QAAL,CAAcmD,YAAd,GAA6BnB,QAAQ,CAACoB,eAAT,CAAyBC,YAAjF;;EAEA,QAAI,CAAC,KAAKhD,kBAAN,IAA4B6C,kBAAhC,EAAoD;EAClD,WAAKlD,QAAL,CAAcsD,KAAd,CAAoBoC,WAApB,GAAqC,KAAKlF,eAA1C;EACD;;EAED,QAAI,KAAKH,kBAAL,IAA2B,CAAC6C,kBAAhC,EAAoD;EAClD,WAAKlD,QAAL,CAAcsD,KAAd,CAAoBqC,YAApB,GAAsC,KAAKnF,eAA3C;EACD;EACF;;WAEDuE,oBAAA,6BAAoB;EAClB,SAAK/E,QAAL,CAAcsD,KAAd,CAAoBoC,WAApB,GAAkC,EAAlC;EACA,SAAK1F,QAAL,CAAcsD,KAAd,CAAoBqC,YAApB,GAAmC,EAAnC;EACD;;WAED1E,kBAAA,2BAAkB;EAChB,QAAM2E,IAAI,GAAG5D,QAAQ,CAACiC,IAAT,CAAc4B,qBAAd,EAAb;EACA,SAAKxF,kBAAL,GAA0ByF,IAAI,CAACC,KAAL,CAAWH,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACK,KAA5B,IAAqCvD,MAAM,CAACwD,UAAtE;EACA,SAAK1F,eAAL,GAAuB,KAAK2F,kBAAL,EAAvB;EACD;;WAEDjF,gBAAA,yBAAgB;EAAA;;EACd,QAAI,KAAKb,kBAAT,EAA6B;EAC3B;EACA;EACA,UAAM+F,YAAY,GAAG,GAAGC,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA0B9G,sBAA1B,CAAd,CAArB;EACA,UAAM+G,aAAa,GAAG,GAAGH,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA0B7G,uBAA1B,CAAd,CAAtB,CAJ2B;;EAO3BjC,MAAAA,qBAAC,CAAC2I,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;EACvC,YAAM+G,aAAa,GAAG/G,OAAO,CAAC0D,KAAR,CAAcqC,YAApC;EACA,YAAMiB,iBAAiB,GAAGnJ,qBAAC,CAACmC,OAAD,CAAD,CAAWiH,GAAX,CAAe,eAAf,CAA1B;EACApJ,QAAAA,qBAAC,CAACmC,OAAD,CAAD,CACGkH,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,OAAI,CAACpG,eAFhE;EAGD,OAND,EAP2B;;EAgB3B/C,MAAAA,qBAAC,CAAC+I,aAAD,CAAD,CAAiBC,IAAjB,CAAsB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;EACxC,YAAMoH,YAAY,GAAGpH,OAAO,CAAC0D,KAAR,CAAc2D,WAAnC;EACA,YAAMC,gBAAgB,GAAGzJ,qBAAC,CAACmC,OAAD,CAAD,CAAWiH,GAAX,CAAe,cAAf,CAAzB;EACApJ,QAAAA,qBAAC,CAACmC,OAAD,CAAD,CACGkH,IADH,CACQ,cADR,EACwBE,YADxB,EAEGH,GAFH,CAEO,cAFP,EAE0BE,UAAU,CAACG,gBAAD,CAAV,GAA+B,OAAI,CAAC1G,eAF9D;EAGD,OAND,EAhB2B;;EAyB3B,UAAMmG,aAAa,GAAG3E,QAAQ,CAACiC,IAAT,CAAcX,KAAd,CAAoBqC,YAA1C;EACA,UAAMiB,iBAAiB,GAAGnJ,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiB4C,GAAjB,CAAqB,eAArB,CAA1B;EACApJ,MAAAA,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CACG6C,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,KAAKpG,eAFhE;EAGD;;EAED/C,IAAAA,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiBO,QAAjB,CAA0BvF,eAA1B;EACD;;WAED+F,kBAAA,2BAAkB;EAChB;EACA,QAAMoB,YAAY,GAAG,GAAGC,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA0B9G,sBAA1B,CAAd,CAArB;EACAhC,IAAAA,qBAAC,CAAC2I,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;EACvC,UAAMuH,OAAO,GAAG1J,qBAAC,CAACmC,OAAD,CAAD,CAAWkH,IAAX,CAAgB,eAAhB,CAAhB;EACArJ,MAAAA,qBAAC,CAACmC,OAAD,CAAD,CAAWiD,UAAX,CAAsB,eAAtB;EACAjD,MAAAA,OAAO,CAAC0D,KAAR,CAAcqC,YAAd,GAA6BwB,OAAO,GAAGA,OAAH,GAAa,EAAjD;EACD,KAJD,EAHgB;;EAUhB,QAAMC,QAAQ,GAAG,GAAGf,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,MAA6B7G,uBAA7B,CAAd,CAAjB;EACAjC,IAAAA,qBAAC,CAAC2J,QAAD,CAAD,CAAYX,IAAZ,CAAiB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;EACnC,UAAMyH,MAAM,GAAG5J,qBAAC,CAACmC,OAAD,CAAD,CAAWkH,IAAX,CAAgB,cAAhB,CAAf;;EACA,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjC5J,QAAAA,qBAAC,CAACmC,OAAD,CAAD,CAAWiH,GAAX,CAAe,cAAf,EAA+BQ,MAA/B,EAAuCxE,UAAvC,CAAkD,cAAlD;EACD;EACF,KALD,EAXgB;;EAmBhB,QAAMsE,OAAO,GAAG1J,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiB6C,IAAjB,CAAsB,eAAtB,CAAhB;EACArJ,IAAAA,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiBpB,UAAjB,CAA4B,eAA5B;EACAb,IAAAA,QAAQ,CAACiC,IAAT,CAAcX,KAAd,CAAoBqC,YAApB,GAAmCwB,OAAO,GAAGA,OAAH,GAAa,EAAvD;EACD;;WAEDhB,qBAAA,8BAAqB;EAAE;EACrB,QAAMmB,SAAS,GAAGtF,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAlB;EACAkC,IAAAA,SAAS,CAACjC,SAAV,GAAsBtG,6BAAtB;EACAiD,IAAAA,QAAQ,CAACiC,IAAT,CAAcC,WAAd,CAA0BoD,SAA1B;EACA,QAAMC,cAAc,GAAGD,SAAS,CAACzB,qBAAV,GAAkC2B,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;EACAzF,IAAAA,QAAQ,CAACiC,IAAT,CAAcyD,WAAd,CAA0BJ,SAA1B;EACA,WAAOC,cAAP;EACD;;;UAIMI,mBAAP,0BAAwB9H,MAAxB,EAAgCa,aAAhC,EAA+C;EAC7C,WAAO,KAAK+F,IAAL,CAAU,YAAY;EAC3B,UAAIK,IAAI,GAAGrJ,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,CAAazJ,QAAb,CAAX;;EACA,UAAMyC,OAAO,gBACRlC,OADQ,EAERH,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,EAFQ,EAGP,OAAOjH,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;EAMA,UAAI,CAACiH,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAInH,KAAJ,CAAU,IAAV,EAAgBG,OAAhB,CAAP;EACArC,QAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,CAAazJ,QAAb,EAAuByJ,IAAvB;EACD;;EAED,UAAI,OAAOjH,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOiH,IAAI,CAACjH,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAI+H,SAAJ,wBAAkC/H,MAAlC,QAAN;EACD;;EAEDiH,QAAAA,IAAI,CAACjH,MAAD,CAAJ,CAAaa,aAAb;EACD,OAND,MAMO,IAAIZ,OAAO,CAAC9B,IAAZ,EAAkB;EACvB8I,QAAAA,IAAI,CAAC9I,IAAL,CAAU0C,aAAV;EACD;EACF,KAtBM,CAAP;EAuBD;;;;0BAreoB;EACnB,aAAOtD,OAAP;EACD;;;0BAEoB;EACnB,aAAOQ,OAAP;EACD;;;;;EAkeH;;;;;;;AAMAH,uBAAC,CAACuE,QAAD,CAAD,CAAYV,EAAZ,CAAezC,oBAAf,EAAqCU,oBAArC,EAA2D,UAAUgC,KAAV,EAAiB;EAAA;;EAC1E,MAAIE,MAAJ;EACA,MAAMoG,QAAQ,GAAGzF,wBAAI,CAAC0F,sBAAL,CAA4B,IAA5B,CAAjB;;EAEA,MAAID,QAAJ,EAAc;EACZpG,IAAAA,MAAM,GAAGO,QAAQ,CAAC9B,aAAT,CAAuB2H,QAAvB,CAAT;EACD;;EAED,MAAMhI,MAAM,GAAGpC,qBAAC,CAACgE,MAAD,CAAD,CAAUqF,IAAV,CAAezJ,QAAf,IACb,QADa,gBAERI,qBAAC,CAACgE,MAAD,CAAD,CAAUqF,IAAV,EAFQ,EAGRrJ,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,EAHQ,CAAf;;EAMA,MAAI,KAAKiB,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnDxG,IAAAA,KAAK,CAACM,cAAN;EACD;;EAED,MAAMmG,OAAO,GAAGvK,qBAAC,CAACgE,MAAD,CAAD,CAAUD,GAAV,CAAcnD,UAAd,EAA0B,UAAAwC,SAAS,EAAI;EACrD,QAAIA,SAAS,CAACG,kBAAV,EAAJ,EAAoC;EAClC;EACA;EACD;;EAEDgH,IAAAA,OAAO,CAACxG,GAAR,CAAYpD,YAAZ,EAA0B,YAAM;EAC9B,UAAIX,qBAAC,CAAC,OAAD,CAAD,CAAQiE,EAAR,CAAW,UAAX,CAAJ,EAA4B;EAC1B,QAAA,OAAI,CAAC3D,KAAL;EACD;EACF,KAJD;EAKD,GAXe,CAAhB;;EAaA4B,EAAAA,KAAK,CAACgI,gBAAN,CAAuBrB,IAAvB,CAA4B7I,qBAAC,CAACgE,MAAD,CAA7B,EAAuC5B,MAAvC,EAA+C,IAA/C;EACD,CAhCD;EAkCA;;;;;;AAMApC,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAawC,KAAK,CAACgI,gBAAnB;AACAlK,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAW8K,WAAX,GAAyBtI,KAAzB;;AACAlC,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAW+K,UAAX,GAAwB,YAAM;EAC5BzK,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOmC,KAAK,CAACgI,gBAAb;EACD,CAHD;;;;;;;;"}