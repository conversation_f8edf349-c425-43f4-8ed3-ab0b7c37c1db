{"version": 3, "file": "tooltip.js", "sources": ["../src/tools/sanitizer.js", "../src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): tools/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nfunction allowedAttribute(attr, allowedAttributeList) {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (unsafeHtml.length === 0) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].slice.call(createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].slice.call(el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './tools/sanitizer'\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_ARROW = '.arrow'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org/)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if ($(this.getTipElement()).hasClass(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    $.removeData(this.element, this.constructor.DATA_KEY)\n\n    $(this.element).off(this.constructor.EVENT_KEY)\n    $(this.element).closest('.modal').off('hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      $(this.tip).remove()\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if ($(this.element).css('display') === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    const showEvent = $.Event(this.constructor.Event.SHOW)\n    if (this.isWithContent() && this._isEnabled) {\n      $(this.element).trigger(showEvent)\n\n      const shadowRoot = Util.findShadowRoot(this.element)\n      const isInTheDom = $.contains(\n        shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement,\n        this.element\n      )\n\n      if (showEvent.isDefaultPrevented() || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = Util.getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        $(tip).addClass(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this.addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      $(tip).data(this.constructor.DATA_KEY, this)\n\n      if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n        $(tip).appendTo(container)\n      }\n\n      $(this.element).trigger(this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      $(tip).addClass(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().on('mouseover', null, $.noop)\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        $(this.element).trigger(this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if ($(this.tip).hasClass(CLASS_NAME_FADE)) {\n        const transitionDuration = Util.getTransitionDurationFromElement(this.tip)\n\n        $(this.tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide(callback) {\n    const tip = this.getTipElement()\n    const hideEvent = $.Event(this.constructor.Event.HIDE)\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      $(this.element).trigger(this.constructor.Event.HIDDEN)\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    $(this.element).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(tip).removeClass(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      $(document.body).children().off('mouseover', null, $.noop)\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if ($(this.tip).hasClass(CLASS_NAME_FADE)) {\n      const transitionDuration = Util.getTransitionDurationFromElement(tip)\n\n      $(tip)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent($(tip.querySelectorAll(SELECTOR_TOOLTIP_INNER)), this.getTitle())\n    $(tip).removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  setElementContent($element, content) {\n    if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n      // Content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (!$(content).parent().is($element)) {\n          $element.empty().append(content)\n        }\n      } else {\n        $element.text($(content).text())\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      $element.html(content)\n    } else {\n      $element.text(content)\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: SELECTOR_ARROW\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (Util.isElement(this.config.container)) {\n      return $(this.config.container)\n    }\n\n    return $(document).find(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        $(this.element).on(\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        $(this.element)\n          .on(eventIn, this.config.selector, event => this._enter(event))\n          .on(eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    $(this.element).closest('.modal').on('hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if ($(context.getTipElement()).hasClass(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = $(this.element).data()\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this.addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    $(tip).removeClass(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Tooltip._jQueryInterface\n$.fn[NAME].Constructor = Tooltip\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tooltip._jQueryInterface\n}\n\nexport default Tooltip\n"], "names": ["uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "toLowerCase", "indexOf", "Boolean", "nodeValue", "match", "regExp", "filter", "attrRegex", "RegExp", "len", "length", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "whitelist<PERSON><PERSON>s", "Object", "keys", "elements", "slice", "call", "body", "querySelectorAll", "el", "el<PERSON>ame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "attributeList", "attributes", "whitelistedAttributes", "concat", "for<PERSON>ach", "removeAttribute", "innerHTML", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "DefaultType", "animation", "template", "title", "trigger", "delay", "html", "selector", "placement", "offset", "container", "fallbackPlacement", "boundary", "sanitize", "popperConfig", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_TOOLTIP_INNER", "SELECTOR_ARROW", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_popper", "_getConfig", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "dataKey", "constructor", "context", "currentTarget", "data", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "hasClass", "dispose", "clearTimeout", "removeData", "off", "closest", "_hideModalHandler", "remove", "destroy", "show", "css", "Error", "showEvent", "isWithContent", "shadowRoot", "<PERSON><PERSON>", "findShadowRoot", "isInTheDom", "contains", "ownerDocument", "documentElement", "isDefaultPrevented", "tipId", "getUID", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "addClass", "attachment", "_getAttachment", "addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "appendTo", "_getPopperConfig", "document", "children", "on", "noop", "complete", "_fixTransition", "prevHoverState", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "hide", "callback", "hideEvent", "_cleanTipClass", "removeClass", "update", "scheduleUpdate", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$element", "content", "nodeType", "j<PERSON>y", "parent", "is", "empty", "append", "text", "getAttribute", "defaultBsConfig", "modifiers", "_getOffset", "flip", "behavior", "arrow", "preventOverflow", "boundariesElement", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "offsets", "isElement", "find", "toUpperCase", "triggers", "split", "eventIn", "eventOut", "_fixTitle", "titleType", "type", "setTimeout", "dataAttributes", "dataAttr", "toString", "typeCheckConfig", "key", "$tip", "tabClass", "join", "popperData", "instance", "popper", "initConfigAnimation", "_jQueryInterface", "each", "_config", "test", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;;;;;;EAOA,IAAMA,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;EAWA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEO,IAAMC,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCD,sBAAvC,CAFyB;EAG9BE,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9BC,EAAAA,CAAC,EAAE,EAlB2B;EAmB9BC,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCP;;;;;;EAKA,IAAMC,gBAAgB,GAAG,6DAAzB;EAEA;;;;;;EAKA,IAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,SAASC,gBAAT,CAA0BC,IAA1B,EAAgCC,oBAAhC,EAAsD;EACpD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcC,WAAd,EAAjB;;EAEA,MAAIH,oBAAoB,CAACI,OAArB,CAA6BH,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;EACjD,QAAIrC,QAAQ,CAACwC,OAAT,CAAiBH,QAAjB,MAA+B,CAAC,CAApC,EAAuC;EACrC,aAAOI,OAAO,CAACN,IAAI,CAACO,SAAL,CAAeC,KAAf,CAAqBX,gBAArB,KAA0CG,IAAI,CAACO,SAAL,CAAeC,KAAf,CAAqBV,gBAArB,CAA3C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,MAAMW,MAAM,GAAGR,oBAAoB,CAACS,MAArB,CAA4B,UAAAC,SAAS;EAAA,WAAIA,SAAS,YAAYC,MAAzB;EAAA,GAArC,CAAf,CAXoD;;EAcpD,OAAK,IAAI7B,CAAC,GAAG,CAAR,EAAW8B,GAAG,GAAGJ,MAAM,CAACK,MAA7B,EAAqC/B,CAAC,GAAG8B,GAAzC,EAA8C9B,CAAC,EAA/C,EAAmD;EACjD,QAAImB,QAAQ,CAACM,KAAT,CAAeC,MAAM,CAAC1B,CAAD,CAArB,CAAJ,EAA+B;EAC7B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD;;EAEM,SAASgC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,MAAIF,UAAU,CAACF,MAAX,KAAsB,CAA1B,EAA6B;EAC3B,WAAOE,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,MAAMG,SAAS,GAAG,IAAIC,MAAM,CAACC,SAAX,EAAlB;EACA,MAAMC,eAAe,GAAGH,SAAS,CAACI,eAAV,CAA0BP,UAA1B,EAAsC,WAAtC,CAAxB;EACA,MAAMQ,aAAa,GAAGC,MAAM,CAACC,IAAP,CAAYT,SAAZ,CAAtB;EACA,MAAMU,QAAQ,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcP,eAAe,CAACQ,IAAhB,CAAqBC,gBAArB,CAAsC,GAAtC,CAAd,CAAjB;;EAZ8D,6BAcrDhD,CAdqD,EAc9C8B,GAd8C;EAe5D,QAAMmB,EAAE,GAAGL,QAAQ,CAAC5C,CAAD,CAAnB;EACA,QAAMkD,MAAM,GAAGD,EAAE,CAAC7B,QAAH,CAAYC,WAAZ,EAAf;;EAEA,QAAIoB,aAAa,CAACnB,OAAd,CAAsB2B,EAAE,CAAC7B,QAAH,CAAYC,WAAZ,EAAtB,MAAqD,CAAC,CAA1D,EAA6D;EAC3D4B,MAAAA,EAAE,CAACE,UAAH,CAAcC,WAAd,CAA0BH,EAA1B;EAEA;EACD;;EAED,QAAMI,aAAa,GAAG,GAAGR,KAAH,CAASC,IAAT,CAAcG,EAAE,CAACK,UAAjB,CAAtB;EACA,QAAMC,qBAAqB,GAAG,GAAGC,MAAH,CAAUtB,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACgB,MAAD,CAAT,IAAqB,EAArD,CAA9B;EAEAG,IAAAA,aAAa,CAACI,OAAd,CAAsB,UAAAxC,IAAI,EAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOsC,qBAAP,CAArB,EAAoD;EAClDN,QAAAA,EAAE,CAACS,eAAH,CAAmBzC,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EA3B4D;;EAc9D,OAAK,IAAIpB,CAAC,GAAG,CAAR,EAAW8B,GAAG,GAAGc,QAAQ,CAACb,MAA/B,EAAuC/B,CAAC,GAAG8B,GAA3C,EAAgD9B,CAAC,EAAjD,EAAqD;EAAA,qBAA5CA,CAA4C;;EAAA,6BAOjD;EAWH;;EAED,SAAOuC,eAAe,CAACQ,IAAhB,CAAqBY,SAA5B;EACD;;;;;;;EC/GD;;;;;;EAMA,IAAMC,IAAI,GAAG,SAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,YAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B;EACA,IAAMO,YAAY,GAAG,YAArB;EACA,IAAMC,kBAAkB,GAAG,IAAIvC,MAAJ,aAAqBsC,YAArB,WAAyC,GAAzC,CAA3B;EACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlBC,EAAAA,OAAO,EAAE,QAJS;EAKlBC,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlBC,EAAAA,QAAQ,EAAE,kBAPQ;EAQlBC,EAAAA,SAAS,EAAE,mBARO;EASlBC,EAAAA,MAAM,EAAE,0BATU;EAUlBC,EAAAA,SAAS,EAAE,0BAVO;EAWlBC,EAAAA,iBAAiB,EAAE,gBAXD;EAYlBC,EAAAA,QAAQ,EAAE,kBAZQ;EAalBC,EAAAA,QAAQ,EAAE,SAbQ;EAclBhD,EAAAA,UAAU,EAAE,iBAdM;EAelBD,EAAAA,SAAS,EAAE,QAfO;EAgBlBkD,EAAAA,YAAY,EAAE;EAhBI,CAApB;EAmBA,IAAMC,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAE,OAHa;EAIpBC,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE;EALc,CAAtB;EAQA,IAAMC,OAAO,GAAG;EACdpB,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACQ,2BADR,GAEQ,yCAJJ;EAKdE,EAAAA,OAAO,EAAE,aALK;EAMdD,EAAAA,KAAK,EAAE,EANO;EAOdE,EAAAA,KAAK,EAAE,CAPO;EAQdC,EAAAA,IAAI,EAAE,KARQ;EASdC,EAAAA,QAAQ,EAAE,KATI;EAUdC,EAAAA,SAAS,EAAE,KAVG;EAWdC,EAAAA,MAAM,EAAE,CAXM;EAYdC,EAAAA,SAAS,EAAE,KAZG;EAadC,EAAAA,iBAAiB,EAAE,MAbL;EAcdC,EAAAA,QAAQ,EAAE,cAdI;EAedC,EAAAA,QAAQ,EAAE,IAfI;EAgBdhD,EAAAA,UAAU,EAAE,IAhBE;EAiBdD,EAAAA,SAAS,EAAElD,gBAjBG;EAkBdoG,EAAAA,YAAY,EAAE;EAlBA,CAAhB;EAqBA,IAAMQ,gBAAgB,GAAG,MAAzB;EACA,IAAMC,eAAe,GAAG,KAAxB;EAEA,IAAMC,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAShC,SADD;EAEZiC,EAAAA,MAAM,aAAWjC,SAFL;EAGZkC,EAAAA,IAAI,WAASlC,SAHD;EAIZmC,EAAAA,KAAK,YAAUnC,SAJH;EAKZoC,EAAAA,QAAQ,eAAapC,SALT;EAMZqC,EAAAA,KAAK,YAAUrC,SANH;EAOZsC,EAAAA,OAAO,cAAYtC,SAPP;EAQZuC,EAAAA,QAAQ,eAAavC,SART;EASZwC,EAAAA,UAAU,iBAAexC,SATb;EAUZyC,EAAAA,UAAU,iBAAezC;EAVb,CAAd;EAaA,IAAM0C,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EAEA,IAAMC,sBAAsB,GAAG,gBAA/B;EACA,IAAMC,cAAc,GAAG,QAAvB;EAEA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,cAAc,GAAG,QAAvB;EAEA;;;;;;MAMMC;EACJ,mBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,QAAI,OAAOC,0BAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAIC,SAAJ,CAAc,kEAAd,CAAN;EACD,KAH0B;;;EAM3B,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAKC,OAAL,GAAe,IAAf,CAV2B;;EAa3B,SAAKR,OAAL,GAAeA,OAAf;EACA,SAAKC,MAAL,GAAc,KAAKQ,UAAL,CAAgBR,MAAhB,CAAd;EACA,SAAKS,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;EACD;;;;;EAgCD;WAEAC,SAAA,kBAAS;EACP,SAAKR,UAAL,GAAkB,IAAlB;EACD;;WAEDS,UAAA,mBAAU;EACR,SAAKT,UAAL,GAAkB,KAAlB;EACD;;WAEDU,gBAAA,yBAAgB;EACd,SAAKV,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;WAEDW,SAAA,gBAAOC,KAAP,EAAc;EACZ,QAAI,CAAC,KAAKZ,UAAV,EAAsB;EACpB;EACD;;EAED,QAAIY,KAAJ,EAAW;EACT,UAAMC,OAAO,GAAG,KAAKC,WAAL,CAAiBtE,QAAjC;EACA,UAAIuE,OAAO,GAAGpE,qBAAC,CAACiE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAAd;;EAEA,UAAI,CAACE,OAAL,EAAc;EACZA,QAAAA,OAAO,GAAG,IAAI,KAAKD,WAAT,CACRF,KAAK,CAACI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;EAIAvE,QAAAA,qBAAC,CAACiE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;EACD;;EAEDA,MAAAA,OAAO,CAACZ,cAAR,CAAuBgB,KAAvB,GAA+B,CAACJ,OAAO,CAACZ,cAAR,CAAuBgB,KAAvD;;EAEA,UAAIJ,OAAO,CAACK,oBAAR,EAAJ,EAAoC;EAClCL,QAAAA,OAAO,CAACM,MAAR,CAAe,IAAf,EAAqBN,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACO,MAAR,CAAe,IAAf,EAAqBP,OAArB;EACD;EACF,KAnBD,MAmBO;EACL,UAAIpE,qBAAC,CAAC,KAAK4E,aAAL,EAAD,CAAD,CAAwBC,QAAxB,CAAiCpC,eAAjC,CAAJ,EAAuD;EACrD,aAAKkC,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;WAEDI,UAAA,mBAAU;EACRC,IAAAA,YAAY,CAAC,KAAKzB,QAAN,CAAZ;EAEAtD,IAAAA,qBAAC,CAACgF,UAAF,CAAa,KAAK/B,OAAlB,EAA2B,KAAKkB,WAAL,CAAiBtE,QAA5C;EAEAG,IAAAA,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBgC,GAAhB,CAAoB,KAAKd,WAAL,CAAiBrE,SAArC;EACAE,IAAAA,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBiC,OAAhB,CAAwB,QAAxB,EAAkCD,GAAlC,CAAsC,eAAtC,EAAuD,KAAKE,iBAA5D;;EAEA,QAAI,KAAKxB,GAAT,EAAc;EACZ3D,MAAAA,qBAAC,CAAC,KAAK2D,GAAN,CAAD,CAAYyB,MAAZ;EACD;;EAED,SAAK/B,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,WAAL,GAAmB,IAAnB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAKC,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa4B,OAAb;EACD;;EAED,SAAK5B,OAAL,GAAe,IAAf;EACA,SAAKR,OAAL,GAAe,IAAf;EACA,SAAKC,MAAL,GAAc,IAAd;EACA,SAAKS,GAAL,GAAW,IAAX;EACD;;WAED2B,OAAA,gBAAO;EAAA;;EACL,QAAItF,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBsC,GAAhB,CAAoB,SAApB,MAAmC,MAAvC,EAA+C;EAC7C,YAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAMC,SAAS,GAAGzF,qBAAC,CAAC6B,KAAF,CAAQ,KAAKsC,WAAL,CAAiBtC,KAAjB,CAAuBG,IAA/B,CAAlB;;EACA,QAAI,KAAK0D,aAAL,MAAwB,KAAKrC,UAAjC,EAA6C;EAC3CrD,MAAAA,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBxC,OAAhB,CAAwBgF,SAAxB;EAEA,UAAME,UAAU,GAAGC,wBAAI,CAACC,cAAL,CAAoB,KAAK5C,OAAzB,CAAnB;EACA,UAAM6C,UAAU,GAAG9F,qBAAC,CAAC+F,QAAF,CACjBJ,UAAU,KAAK,IAAf,GAAsBA,UAAtB,GAAmC,KAAK1C,OAAL,CAAa+C,aAAb,CAA2BC,eAD7C,EAEjB,KAAKhD,OAFY,CAAnB;;EAKA,UAAIwC,SAAS,CAACS,kBAAV,MAAkC,CAACJ,UAAvC,EAAmD;EACjD;EACD;;EAED,UAAMnC,GAAG,GAAG,KAAKiB,aAAL,EAAZ;EACA,UAAMuB,KAAK,GAAGP,wBAAI,CAACQ,MAAL,CAAY,KAAKjC,WAAL,CAAiBxE,IAA7B,CAAd;EAEAgE,MAAAA,GAAG,CAAC0C,YAAJ,CAAiB,IAAjB,EAAuBF,KAAvB;EACA,WAAKlD,OAAL,CAAaoD,YAAb,CAA0B,kBAA1B,EAA8CF,KAA9C;EAEA,WAAKG,UAAL;;EAEA,UAAI,KAAKpD,MAAL,CAAY5C,SAAhB,EAA2B;EACzBN,QAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAO4C,QAAP,CAAgB/D,eAAhB;EACD;;EAED,UAAM3B,SAAS,GAAG,OAAO,KAAKqC,MAAL,CAAYrC,SAAnB,KAAiC,UAAjC,GAChB,KAAKqC,MAAL,CAAYrC,SAAZ,CAAsBhC,IAAtB,CAA2B,IAA3B,EAAiC8E,GAAjC,EAAsC,KAAKV,OAA3C,CADgB,GAEhB,KAAKC,MAAL,CAAYrC,SAFd;;EAIA,UAAM2F,UAAU,GAAG,KAAKC,cAAL,CAAoB5F,SAApB,CAAnB;;EACA,WAAK6F,kBAAL,CAAwBF,UAAxB;;EAEA,UAAMzF,SAAS,GAAG,KAAK4F,aAAL,EAAlB;;EACA3G,MAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAOW,IAAP,CAAY,KAAKH,WAAL,CAAiBtE,QAA7B,EAAuC,IAAvC;;EAEA,UAAI,CAACG,qBAAC,CAAC+F,QAAF,CAAW,KAAK9C,OAAL,CAAa+C,aAAb,CAA2BC,eAAtC,EAAuD,KAAKtC,GAA5D,CAAL,EAAuE;EACrE3D,QAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAOiD,QAAP,CAAgB7F,SAAhB;EACD;;EAEDf,MAAAA,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBxC,OAAhB,CAAwB,KAAK0D,WAAL,CAAiBtC,KAAjB,CAAuBK,QAA/C;EAEA,WAAKuB,OAAL,GAAe,IAAIN,0BAAJ,CAAW,KAAKF,OAAhB,EAAyBU,GAAzB,EAA8B,KAAKkD,gBAAL,CAAsBL,UAAtB,CAA9B,CAAf;EAEAxG,MAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAO4C,QAAP,CAAgB9D,eAAhB,EA3C2C;EA8C3C;EACA;EACA;;EACA,UAAI,kBAAkBqE,QAAQ,CAACb,eAA/B,EAAgD;EAC9CjG,QAAAA,qBAAC,CAAC8G,QAAQ,CAAChI,IAAV,CAAD,CAAiBiI,QAAjB,GAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkDhH,qBAAC,CAACiH,IAApD;EACD;;EAED,UAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,YAAI,KAAI,CAAChE,MAAL,CAAY5C,SAAhB,EAA2B;EACzB,UAAA,KAAI,CAAC6G,cAAL;EACD;;EAED,YAAMC,cAAc,GAAG,KAAI,CAAC7D,WAA5B;EACA,QAAA,KAAI,CAACA,WAAL,GAAmB,IAAnB;EAEAvD,QAAAA,qBAAC,CAAC,KAAI,CAACiD,OAAN,CAAD,CAAgBxC,OAAhB,CAAwB,KAAI,CAAC0D,WAAL,CAAiBtC,KAAjB,CAAuBI,KAA/C;;EAEA,YAAImF,cAAc,KAAKxF,eAAvB,EAAwC;EACtC,UAAA,KAAI,CAAC+C,MAAL,CAAY,IAAZ,EAAkB,KAAlB;EACD;EACF,OAbD;;EAeA,UAAI3E,qBAAC,CAAC,KAAK2D,GAAN,CAAD,CAAYkB,QAAZ,CAAqBrC,eAArB,CAAJ,EAA2C;EACzC,YAAM6E,kBAAkB,GAAGzB,wBAAI,CAAC0B,gCAAL,CAAsC,KAAK3D,GAA3C,CAA3B;EAEA3D,QAAAA,qBAAC,CAAC,KAAK2D,GAAN,CAAD,CACG4D,GADH,CACO3B,wBAAI,CAAC4B,cADZ,EAC4BN,QAD5B,EAEGO,oBAFH,CAEwBJ,kBAFxB;EAGD,OAND,MAMO;EACLH,QAAAA,QAAQ;EACT;EACF;EACF;;WAEDQ,OAAA,cAAKC,QAAL,EAAe;EAAA;;EACb,QAAMhE,GAAG,GAAG,KAAKiB,aAAL,EAAZ;EACA,QAAMgD,SAAS,GAAG5H,qBAAC,CAAC6B,KAAF,CAAQ,KAAKsC,WAAL,CAAiBtC,KAAjB,CAAuBC,IAA/B,CAAlB;;EACA,QAAMoF,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAI,MAAI,CAAC3D,WAAL,KAAqB5B,gBAArB,IAAyCgC,GAAG,CAACzE,UAAjD,EAA6D;EAC3DyE,QAAAA,GAAG,CAACzE,UAAJ,CAAeC,WAAf,CAA2BwE,GAA3B;EACD;;EAED,MAAA,MAAI,CAACkE,cAAL;;EACA,MAAA,MAAI,CAAC5E,OAAL,CAAaxD,eAAb,CAA6B,kBAA7B;;EACAO,MAAAA,qBAAC,CAAC,MAAI,CAACiD,OAAN,CAAD,CAAgBxC,OAAhB,CAAwB,MAAI,CAAC0D,WAAL,CAAiBtC,KAAjB,CAAuBE,MAA/C;;EACA,UAAI,MAAI,CAAC0B,OAAL,KAAiB,IAArB,EAA2B;EACzB,QAAA,MAAI,CAACA,OAAL,CAAa4B,OAAb;EACD;;EAED,UAAIsC,QAAJ,EAAc;EACZA,QAAAA,QAAQ;EACT;EACF,KAfD;;EAiBA3H,IAAAA,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBxC,OAAhB,CAAwBmH,SAAxB;;EAEA,QAAIA,SAAS,CAAC1B,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAEDlG,IAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAOmE,WAAP,CAAmBrF,eAAnB,EA1Ba;EA6Bb;;EACA,QAAI,kBAAkBqE,QAAQ,CAACb,eAA/B,EAAgD;EAC9CjG,MAAAA,qBAAC,CAAC8G,QAAQ,CAAChI,IAAV,CAAD,CAAiBiI,QAAjB,GAA4B9B,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmDjF,qBAAC,CAACiH,IAArD;EACD;;EAED,SAAKzD,cAAL,CAAoBV,aAApB,IAAqC,KAArC;EACA,SAAKU,cAAL,CAAoBX,aAApB,IAAqC,KAArC;EACA,SAAKW,cAAL,CAAoBZ,aAApB,IAAqC,KAArC;;EAEA,QAAI5C,qBAAC,CAAC,KAAK2D,GAAN,CAAD,CAAYkB,QAAZ,CAAqBrC,eAArB,CAAJ,EAA2C;EACzC,UAAM6E,kBAAkB,GAAGzB,wBAAI,CAAC0B,gCAAL,CAAsC3D,GAAtC,CAA3B;EAEA3D,MAAAA,qBAAC,CAAC2D,GAAD,CAAD,CACG4D,GADH,CACO3B,wBAAI,CAAC4B,cADZ,EAC4BN,QAD5B,EAEGO,oBAFH,CAEwBJ,kBAFxB;EAGD,KAND,MAMO;EACLH,MAAAA,QAAQ;EACT;;EAED,SAAK3D,WAAL,GAAmB,EAAnB;EACD;;WAEDwE,SAAA,kBAAS;EACP,QAAI,KAAKtE,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAauE,cAAb;EACD;EACF;;;WAIDtC,gBAAA,yBAAgB;EACd,WAAOpI,OAAO,CAAC,KAAK2K,QAAL,EAAD,CAAd;EACD;;WAEDvB,qBAAA,4BAAmBF,UAAnB,EAA+B;EAC7BxG,IAAAA,qBAAC,CAAC,KAAK4E,aAAL,EAAD,CAAD,CAAwB2B,QAAxB,CAAoCrG,YAApC,SAAoDsG,UAApD;EACD;;WAED5B,gBAAA,yBAAgB;EACd,SAAKjB,GAAL,GAAW,KAAKA,GAAL,IAAY3D,qBAAC,CAAC,KAAKkD,MAAL,CAAY3C,QAAb,CAAD,CAAwB,CAAxB,CAAvB;EACA,WAAO,KAAKoD,GAAZ;EACD;;WAED2C,aAAA,sBAAa;EACX,QAAM3C,GAAG,GAAG,KAAKiB,aAAL,EAAZ;EACA,SAAKsD,iBAAL,CAAuBlI,qBAAC,CAAC2D,GAAG,CAAC5E,gBAAJ,CAAqB2D,sBAArB,CAAD,CAAxB,EAAwE,KAAKuF,QAAL,EAAxE;EACAjI,IAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAOmE,WAAP,CAAsBtF,eAAtB,SAAyCC,eAAzC;EACD;;WAEDyF,oBAAA,2BAAkBC,QAAlB,EAA4BC,OAA5B,EAAqC;EACnC,QAAI,OAAOA,OAAP,KAAmB,QAAnB,KAAgCA,OAAO,CAACC,QAAR,IAAoBD,OAAO,CAACE,MAA5D,CAAJ,EAAyE;EACvE;EACA,UAAI,KAAKpF,MAAL,CAAYvC,IAAhB,EAAsB;EACpB,YAAI,CAACX,qBAAC,CAACoI,OAAD,CAAD,CAAWG,MAAX,GAAoBC,EAApB,CAAuBL,QAAvB,CAAL,EAAuC;EACrCA,UAAAA,QAAQ,CAACM,KAAT,GAAiBC,MAAjB,CAAwBN,OAAxB;EACD;EACF,OAJD,MAIO;EACLD,QAAAA,QAAQ,CAACQ,IAAT,CAAc3I,qBAAC,CAACoI,OAAD,CAAD,CAAWO,IAAX,EAAd;EACD;;EAED;EACD;;EAED,QAAI,KAAKzF,MAAL,CAAYvC,IAAhB,EAAsB;EACpB,UAAI,KAAKuC,MAAL,CAAYhC,QAAhB,EAA0B;EACxBkH,QAAAA,OAAO,GAAGrK,YAAY,CAACqK,OAAD,EAAU,KAAKlF,MAAL,CAAYjF,SAAtB,EAAiC,KAAKiF,MAAL,CAAYhF,UAA7C,CAAtB;EACD;;EAEDiK,MAAAA,QAAQ,CAACxH,IAAT,CAAcyH,OAAd;EACD,KAND,MAMO;EACLD,MAAAA,QAAQ,CAACQ,IAAT,CAAcP,OAAd;EACD;EACF;;WAEDH,WAAA,oBAAW;EACT,QAAIzH,KAAK,GAAG,KAAKyC,OAAL,CAAa2F,YAAb,CAA0B,qBAA1B,CAAZ;;EAEA,QAAI,CAACpI,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAK0C,MAAL,CAAY1C,KAAnB,KAA6B,UAA7B,GACN,KAAK0C,MAAL,CAAY1C,KAAZ,CAAkB3B,IAAlB,CAAuB,KAAKoE,OAA5B,CADM,GAEN,KAAKC,MAAL,CAAY1C,KAFd;EAGD;;EAED,WAAOA,KAAP;EACD;;;WAIDqG,mBAAA,0BAAiBL,UAAjB,EAA6B;EAAA;;EAC3B,QAAMqC,eAAe,GAAG;EACtBhI,MAAAA,SAAS,EAAE2F,UADW;EAEtBsC,MAAAA,SAAS,EAAE;EACThI,QAAAA,MAAM,EAAE,KAAKiI,UAAL,EADC;EAETC,QAAAA,IAAI,EAAE;EACJC,UAAAA,QAAQ,EAAE,KAAK/F,MAAL,CAAYlC;EADlB,SAFG;EAKTkI,QAAAA,KAAK,EAAE;EACLjG,UAAAA,OAAO,EAAEN;EADJ,SALE;EAQTwG,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKlG,MAAL,CAAYjC;EADhB;EARR,OAFW;EActBoI,MAAAA,QAAQ,EAAE,kBAAA/E,IAAI,EAAI;EAChB,YAAIA,IAAI,CAACgF,iBAAL,KAA2BhF,IAAI,CAACzD,SAApC,EAA+C;EAC7C,UAAA,MAAI,CAAC0I,4BAAL,CAAkCjF,IAAlC;EACD;EACF,OAlBqB;EAmBtBkF,MAAAA,QAAQ,EAAE,kBAAAlF,IAAI;EAAA,eAAI,MAAI,CAACiF,4BAAL,CAAkCjF,IAAlC,CAAJ;EAAA;EAnBQ,KAAxB;EAsBA,wBACKuE,eADL,EAEK,KAAK3F,MAAL,CAAY/B,YAFjB;EAID;;WAED4H,aAAA,sBAAa;EAAA;;EACX,QAAMjI,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKoC,MAAL,CAAYpC,MAAnB,KAA8B,UAAlC,EAA8C;EAC5CA,MAAAA,MAAM,CAACb,EAAP,GAAY,UAAAqE,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAACmF,OAAL,gBACKnF,IAAI,CAACmF,OADV,EAEM,MAAI,CAACvG,MAAL,CAAYpC,MAAZ,CAAmBwD,IAAI,CAACmF,OAAxB,EAAiC,MAAI,CAACxG,OAAtC,KAAkD,EAFxD;EAKA,eAAOqB,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACLxD,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKoC,MAAL,CAAYpC,MAA5B;EACD;;EAED,WAAOA,MAAP;EACD;;WAED6F,gBAAA,yBAAgB;EACd,QAAI,KAAKzD,MAAL,CAAYnC,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAO+F,QAAQ,CAAChI,IAAhB;EACD;;EAED,QAAI8G,wBAAI,CAAC8D,SAAL,CAAe,KAAKxG,MAAL,CAAYnC,SAA3B,CAAJ,EAA2C;EACzC,aAAOf,qBAAC,CAAC,KAAKkD,MAAL,CAAYnC,SAAb,CAAR;EACD;;EAED,WAAOf,qBAAC,CAAC8G,QAAD,CAAD,CAAY6C,IAAZ,CAAiB,KAAKzG,MAAL,CAAYnC,SAA7B,CAAP;EACD;;WAED0F,iBAAA,wBAAe5F,SAAf,EAA0B;EACxB,WAAOO,aAAa,CAACP,SAAS,CAAC+I,WAAV,EAAD,CAApB;EACD;;WAEDhG,gBAAA,yBAAgB;EAAA;;EACd,QAAMiG,QAAQ,GAAG,KAAK3G,MAAL,CAAYzC,OAAZ,CAAoBqJ,KAApB,CAA0B,GAA1B,CAAjB;EAEAD,IAAAA,QAAQ,CAACrK,OAAT,CAAiB,UAAAiB,OAAO,EAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBT,QAAAA,qBAAC,CAAC,MAAI,CAACiD,OAAN,CAAD,CAAgB+D,EAAhB,CACE,MAAI,CAAC7C,WAAL,CAAiBtC,KAAjB,CAAuBM,KADzB,EAEE,MAAI,CAACe,MAAL,CAAYtC,QAFd,EAGE,UAAAqD,KAAK;EAAA,iBAAI,MAAI,CAACD,MAAL,CAAYC,KAAZ,CAAJ;EAAA,SAHP;EAKD,OAND,MAMO,IAAIxD,OAAO,KAAKsC,cAAhB,EAAgC;EACrC,YAAMgH,OAAO,GAAGtJ,OAAO,KAAKmC,aAAZ,GACd,MAAI,CAACuB,WAAL,CAAiBtC,KAAjB,CAAuBS,UADT,GAEd,MAAI,CAAC6B,WAAL,CAAiBtC,KAAjB,CAAuBO,OAFzB;EAGA,YAAM4H,QAAQ,GAAGvJ,OAAO,KAAKmC,aAAZ,GACf,MAAI,CAACuB,WAAL,CAAiBtC,KAAjB,CAAuBU,UADR,GAEf,MAAI,CAAC4B,WAAL,CAAiBtC,KAAjB,CAAuBQ,QAFzB;EAIArC,QAAAA,qBAAC,CAAC,MAAI,CAACiD,OAAN,CAAD,CACG+D,EADH,CACM+C,OADN,EACe,MAAI,CAAC7G,MAAL,CAAYtC,QAD3B,EACqC,UAAAqD,KAAK;EAAA,iBAAI,MAAI,CAACS,MAAL,CAAYT,KAAZ,CAAJ;EAAA,SAD1C,EAEG+C,EAFH,CAEMgD,QAFN,EAEgB,MAAI,CAAC9G,MAAL,CAAYtC,QAF5B,EAEsC,UAAAqD,KAAK;EAAA,iBAAI,MAAI,CAACU,MAAL,CAAYV,KAAZ,CAAJ;EAAA,SAF3C;EAGD;EACF,KAnBD;;EAqBA,SAAKkB,iBAAL,GAAyB,YAAM;EAC7B,UAAI,MAAI,CAAClC,OAAT,EAAkB;EAChB,QAAA,MAAI,CAACyE,IAAL;EACD;EACF,KAJD;;EAMA1H,IAAAA,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBiC,OAAhB,CAAwB,QAAxB,EAAkC8B,EAAlC,CAAqC,eAArC,EAAsD,KAAK7B,iBAA3D;;EAEA,QAAI,KAAKjC,MAAL,CAAYtC,QAAhB,EAA0B;EACxB,WAAKsC,MAAL,gBACK,KAAKA,MADV;EAEEzC,QAAAA,OAAO,EAAE,QAFX;EAGEG,QAAAA,QAAQ,EAAE;EAHZ;EAKD,KAND,MAMO;EACL,WAAKqJ,SAAL;EACD;EACF;;WAEDA,YAAA,qBAAY;EACV,QAAMC,SAAS,GAAG,OAAO,KAAKjH,OAAL,CAAa2F,YAAb,CAA0B,qBAA1B,CAAzB;;EAEA,QAAI,KAAK3F,OAAL,CAAa2F,YAAb,CAA0B,OAA1B,KAAsCsB,SAAS,KAAK,QAAxD,EAAkE;EAChE,WAAKjH,OAAL,CAAaoD,YAAb,CACE,qBADF,EAEE,KAAKpD,OAAL,CAAa2F,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;EAKA,WAAK3F,OAAL,CAAaoD,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;EACD;EACF;;WAED3B,SAAA,gBAAOT,KAAP,EAAcG,OAAd,EAAuB;EACrB,QAAMF,OAAO,GAAG,KAAKC,WAAL,CAAiBtE,QAAjC;EACAuE,IAAAA,OAAO,GAAGA,OAAO,IAAIpE,qBAAC,CAACiE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB;;EAEA,QAAI,CAACE,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKD,WAAT,CACRF,KAAK,CAACI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;EAIAvE,MAAAA,qBAAC,CAACiE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;EACD;;EAED,QAAIH,KAAJ,EAAW;EACTG,MAAAA,OAAO,CAACZ,cAAR,CACES,KAAK,CAACkG,IAAN,KAAe,SAAf,GAA2BtH,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAI5C,qBAAC,CAACoE,OAAO,CAACQ,aAAR,EAAD,CAAD,CAA2BC,QAA3B,CAAoCpC,eAApC,KAAwD2B,OAAO,CAACb,WAAR,KAAwB5B,gBAApF,EAAsG;EACpGyC,MAAAA,OAAO,CAACb,WAAR,GAAsB5B,gBAAtB;EACA;EACD;;EAEDoD,IAAAA,YAAY,CAACX,OAAO,CAACd,QAAT,CAAZ;EAEAc,IAAAA,OAAO,CAACb,WAAR,GAAsB5B,gBAAtB;;EAEA,QAAI,CAACyC,OAAO,CAAClB,MAAR,CAAexC,KAAhB,IAAyB,CAAC0D,OAAO,CAAClB,MAAR,CAAexC,KAAf,CAAqB4E,IAAnD,EAAyD;EACvDlB,MAAAA,OAAO,CAACkB,IAAR;EACA;EACD;;EAEDlB,IAAAA,OAAO,CAACd,QAAR,GAAmB8G,UAAU,CAAC,YAAM;EAClC,UAAIhG,OAAO,CAACb,WAAR,KAAwB5B,gBAA5B,EAA8C;EAC5CyC,QAAAA,OAAO,CAACkB,IAAR;EACD;EACF,KAJ4B,EAI1BlB,OAAO,CAAClB,MAAR,CAAexC,KAAf,CAAqB4E,IAJK,CAA7B;EAKD;;WAEDX,SAAA,gBAAOV,KAAP,EAAcG,OAAd,EAAuB;EACrB,QAAMF,OAAO,GAAG,KAAKC,WAAL,CAAiBtE,QAAjC;EACAuE,IAAAA,OAAO,GAAGA,OAAO,IAAIpE,qBAAC,CAACiE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB;;EAEA,QAAI,CAACE,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKD,WAAT,CACRF,KAAK,CAACI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;EAIAvE,MAAAA,qBAAC,CAACiE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;EACD;;EAED,QAAIH,KAAJ,EAAW;EACTG,MAAAA,OAAO,CAACZ,cAAR,CACES,KAAK,CAACkG,IAAN,KAAe,UAAf,GAA4BtH,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;EAGD;;EAED,QAAIwB,OAAO,CAACK,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDM,IAAAA,YAAY,CAACX,OAAO,CAACd,QAAT,CAAZ;EAEAc,IAAAA,OAAO,CAACb,WAAR,GAAsB3B,eAAtB;;EAEA,QAAI,CAACwC,OAAO,CAAClB,MAAR,CAAexC,KAAhB,IAAyB,CAAC0D,OAAO,CAAClB,MAAR,CAAexC,KAAf,CAAqBgH,IAAnD,EAAyD;EACvDtD,MAAAA,OAAO,CAACsD,IAAR;EACA;EACD;;EAEDtD,IAAAA,OAAO,CAACd,QAAR,GAAmB8G,UAAU,CAAC,YAAM;EAClC,UAAIhG,OAAO,CAACb,WAAR,KAAwB3B,eAA5B,EAA6C;EAC3CwC,QAAAA,OAAO,CAACsD,IAAR;EACD;EACF,KAJ4B,EAI1BtD,OAAO,CAAClB,MAAR,CAAexC,KAAf,CAAqBgH,IAJK,CAA7B;EAKD;;WAEDjD,uBAAA,gCAAuB;EACrB,SAAK,IAAMhE,OAAX,IAAsB,KAAK+C,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoB/C,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;WAEDiD,aAAA,oBAAWR,MAAX,EAAmB;EACjB,QAAMmH,cAAc,GAAGrK,qBAAC,CAAC,KAAKiD,OAAN,CAAD,CAAgBqB,IAAhB,EAAvB;EAEA7F,IAAAA,MAAM,CAACC,IAAP,CAAY2L,cAAZ,EACG7K,OADH,CACW,UAAA8K,QAAQ,EAAI;EACnB,UAAIlK,qBAAqB,CAAC/C,OAAtB,CAA8BiN,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;EAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KALH;EAOApH,IAAAA,MAAM,gBACD,KAAKiB,WAAL,CAAiBzC,OADhB,EAED2I,cAFC,EAGA,OAAOnH,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;;EAMA,QAAI,OAAOA,MAAM,CAACxC,KAAd,KAAwB,QAA5B,EAAsC;EACpCwC,MAAAA,MAAM,CAACxC,KAAP,GAAe;EACb4E,QAAAA,IAAI,EAAEpC,MAAM,CAACxC,KADA;EAEbgH,QAAAA,IAAI,EAAExE,MAAM,CAACxC;EAFA,OAAf;EAID;;EAED,QAAI,OAAOwC,MAAM,CAAC1C,KAAd,KAAwB,QAA5B,EAAsC;EACpC0C,MAAAA,MAAM,CAAC1C,KAAP,GAAe0C,MAAM,CAAC1C,KAAP,CAAa+J,QAAb,EAAf;EACD;;EAED,QAAI,OAAOrH,MAAM,CAACkF,OAAd,KAA0B,QAA9B,EAAwC;EACtClF,MAAAA,MAAM,CAACkF,OAAP,GAAiBlF,MAAM,CAACkF,OAAP,CAAemC,QAAf,EAAjB;EACD;;EAED3E,IAAAA,wBAAI,CAAC4E,eAAL,CACE7K,IADF,EAEEuD,MAFF,EAGE,KAAKiB,WAAL,CAAiB9D,WAHnB;;EAMA,QAAI6C,MAAM,CAAChC,QAAX,EAAqB;EACnBgC,MAAAA,MAAM,CAAC3C,QAAP,GAAkBxC,YAAY,CAACmF,MAAM,CAAC3C,QAAR,EAAkB2C,MAAM,CAACjF,SAAzB,EAAoCiF,MAAM,CAAChF,UAA3C,CAA9B;EACD;;EAED,WAAOgF,MAAP;EACD;;WAEDqB,qBAAA,8BAAqB;EACnB,QAAMrB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,IAAMuH,GAAX,IAAkB,KAAKvH,MAAvB,EAA+B;EAC7B,YAAI,KAAKiB,WAAL,CAAiBzC,OAAjB,CAAyB+I,GAAzB,MAAkC,KAAKvH,MAAL,CAAYuH,GAAZ,CAAtC,EAAwD;EACtDvH,UAAAA,MAAM,CAACuH,GAAD,CAAN,GAAc,KAAKvH,MAAL,CAAYuH,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOvH,MAAP;EACD;;WAED2E,iBAAA,0BAAiB;EACf,QAAM6C,IAAI,GAAG1K,qBAAC,CAAC,KAAK4E,aAAL,EAAD,CAAd;EACA,QAAM+F,QAAQ,GAAGD,IAAI,CAAC1N,IAAL,CAAU,OAAV,EAAmBQ,KAAnB,CAAyB2C,kBAAzB,CAAjB;;EACA,QAAIwK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC7M,MAAlC,EAA0C;EACxC4M,MAAAA,IAAI,CAAC5C,WAAL,CAAiB6C,QAAQ,CAACC,IAAT,CAAc,EAAd,CAAjB;EACD;EACF;;WAEDrB,+BAAA,sCAA6BsB,UAA7B,EAAyC;EACvC,SAAKlH,GAAL,GAAWkH,UAAU,CAACC,QAAX,CAAoBC,MAA/B;;EACA,SAAKlD,cAAL;;EACA,SAAKnB,kBAAL,CAAwB,KAAKD,cAAL,CAAoBoE,UAAU,CAAChK,SAA/B,CAAxB;EACD;;WAEDsG,iBAAA,0BAAiB;EACf,QAAMxD,GAAG,GAAG,KAAKiB,aAAL,EAAZ;EACA,QAAMoG,mBAAmB,GAAG,KAAK9H,MAAL,CAAY5C,SAAxC;;EAEA,QAAIqD,GAAG,CAACiF,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;EAC5C;EACD;;EAED5I,IAAAA,qBAAC,CAAC2D,GAAD,CAAD,CAAOmE,WAAP,CAAmBtF,eAAnB;EACA,SAAKU,MAAL,CAAY5C,SAAZ,GAAwB,KAAxB;EACA,SAAKoH,IAAL;EACA,SAAKpC,IAAL;EACA,SAAKpC,MAAL,CAAY5C,SAAZ,GAAwB0K,mBAAxB;EACD;;;YAIMC,mBAAP,0BAAwB/H,MAAxB,EAAgC;EAC9B,WAAO,KAAKgI,IAAL,CAAU,YAAY;EAC3B,UAAM/C,QAAQ,GAAGnI,qBAAC,CAAC,IAAD,CAAlB;EACA,UAAIsE,IAAI,GAAG6D,QAAQ,CAAC7D,IAAT,CAAczE,QAAd,CAAX;;EACA,UAAMsL,OAAO,GAAG,OAAOjI,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACoB,IAAD,IAAS,eAAe8G,IAAf,CAAoBlI,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACoB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAItB,OAAJ,CAAY,IAAZ,EAAkBmI,OAAlB,CAAP;EACAhD,QAAAA,QAAQ,CAAC7D,IAAT,CAAczE,QAAd,EAAwByE,IAAxB;EACD;;EAED,UAAI,OAAOpB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOoB,IAAI,CAACpB,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIE,SAAJ,wBAAkCF,MAAlC,QAAN;EACD;;EAEDoB,QAAAA,IAAI,CAACpB,MAAD,CAAJ;EACD;EACF,KArBM,CAAP;EAsBD;;;;0BAhnBoB;EACnB,aAAOtD,OAAP;EACD;;;0BAEoB;EACnB,aAAO8B,OAAP;EACD;;;0BAEiB;EAChB,aAAO/B,IAAP;EACD;;;0BAEqB;EACpB,aAAOE,QAAP;EACD;;;0BAEkB;EACjB,aAAOgC,KAAP;EACD;;;0BAEsB;EACrB,aAAO/B,SAAP;EACD;;;0BAEwB;EACvB,aAAOO,WAAP;EACD;;;;;EAylBH;;;;;;;AAMAL,uBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaqD,OAAO,CAACiI,gBAArB;AACAjL,uBAAC,CAACC,EAAF,CAAKN,IAAL,EAAW0L,WAAX,GAAyBrI,OAAzB;;AACAhD,uBAAC,CAACC,EAAF,CAAKN,IAAL,EAAW2L,UAAX,GAAwB,YAAM;EAC5BtL,EAAAA,qBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAOiD,OAAO,CAACiI,gBAAf;EACD,CAHD;;;;;;;;"}