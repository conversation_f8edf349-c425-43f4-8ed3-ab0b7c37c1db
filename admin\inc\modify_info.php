<?php if($_SESSION['admin_id']==True){ ?>
<?php

 include'../function.php';
  $id = $_GET['id'];

  if(!isset($_POST['submit']))
  {

$result = mysql_query("SELECT * FROM employer_mail WHERE id ='$id'");
        while($row = mysql_fetch_array($result))
              {
$email       = $row['email'];
$company     = $row['company'];
$website     = $row['website'];
$address     = $row['location'];
$phone       = $row['phone'];
$image       = $row['image'];
$keywords    = $row['keywords'];
$facebook    = $row['facebook'];
$twitter     = $row['twitter'];
$description = $row['description'];
}
  }
?>
  <a class="style1" href="cpanel.php?p=mail_add"><img src="../images/cpanel/database_add.png" width="48" height="48" border="0"></a>
<a class="style1" href="cpanel.php?p=mail_list"><img src="../images/cpanel/shoplist.gif" width="48" height="48" border="0"></a>
   <br/><br/>
<form action='' method='POST'  enctype="multipart/form-data">
<table width="10%" cellspacing="0" cellpadding="0" class="frame">

    <td><table width="100%" border="0" cellpadding="8" cellspacing="0">

  <tr><td >Company:</td>

          <td><input type='text' name='company' placeholder='ExploreBS' size='30' value="<? echo $company; ?>"></td>

      </tr>

      <tr><td>Phone:</td>

          <td><input type='text' name='phone' placeholder='************' value="<? echo $phone; ?>"></td>

      </tr>
      <tr>
      <td>Location:</td>

          <td><input type='text' name='address' placeholder='Bay Street #854' value="<? echo $address; ?>"></td>
      </tr>
      <tr>

       <tr><td>Email:</td>

           <td><input type='text' name='email' placeholder='<EMAIL>' size='30'value="<? echo $email; ?>" ></td>

        </tr>

             <tr><td>Website:</td>

           <td><input type='text' name='website' placeholder='www.explorebs.com' size='30' value="<? echo $website; ?>"></td>

        </tr>
        </tr>
             <tr><td>Keywords:</td>

           <td><input type='text' name='keywords' placeholder='Search terms' size='30' value="<? echo $keywords; ?>"></td>

        </tr>
        </tr>

             <tr><td>Facebook url:</td>

           <td><input type='text' name='facebook' placeholder='facebook.com/explorebs'size='30' value="<? echo $facebook; ?>"></td>

        </tr>

             <tr><td>Twitter url:</td>

           <td><input type='text' name='twitter' placeholder='twitter.com/explorebs' size='30' value="<? echo $twitter; ?>"></td>

        </tr>
        <tr>
        <td>Description:</td>

           <td><textarea type='text' name='description' cols='35' rows='4' size='30' value=""><? echo $description; ?></textarea></td>

        </tr>

        <tr><td>Image:</td>

           <td><input type='file' name='myfile' size='30' value=""><?echo $image;?></td>

        </tr>
      <tr>

      <td ><input type="hidden"   name="id"      value="<?php echo $_GET['id']; ?>"/>

      </td>

          <td><input type='submit' name='update' value='<-- Add Your Business -->' class='button'>

          </td>

      </tbody></table></div></tbody></table></form></div>
<div style="width: 100%;">
<?php
$submit = $_POST['update'];

$email            = $_POST['email'];
$phone            = $_POST['phone'];
$address          = $_POST['address'];
$company          = $_POST['company'];
$website          = $_POST['website'];
$keywords         = $_POST['keywords'];
$description      = $_POST['description'];

$facebook          = $_POST['facebook'];
$twitter           = $_POST['twitter'];

$name             = $_FILES['myfile']['name'];
$tmp_name         = $_FILES['myfile']['tmp_name'];

$id               = $_GET['id'];

if($submit)
{

// conncet to the database
        include'../function.php';

         // Start Upload process
         $location = "../image/$name";

         move_uploaded_file($tmp_name, $location);

        //insert data
        $insert = mysql_query("UPDATE employer_mail SET email    ='$email',
                                                        phone    ='$phone',
                                                        location  ='$address',
                                                        company  ='$company',
                                                        website  ='$website',
                                                        image    ='$location',
                                                        facebook ='$facebook',
                                                        twitter  ='$twitter',
                                                        keywords ='$keywords',
                                                        description ='$description'
                                                 WHERE  id       = '$id'");
echo"
<div class='resultItem resultItemMouseOver resultItemOff'>
				<div class='addImage'>

<a href='http://$url'>";
if(($image=="")||($image=="image/")||($image=="../image/"))
{echo"<img border='0' src='../image/EBS/explorebsicon.png' width='110'>";}
else
{echo"<img border='0' src='../$image' width='110'>";}echo"
</a>
</div>
<div class='addTekst'>
<div class='starRatingSearch star0'><font color='#5081F7'>$title</font></div>

<br clear='left'>
";if($description==True){echo"
<font style='font-size:12px' color='red'><a class='cat'>";echo substr("$description" ,0,250); echo"...</a></font>
";}echo"
<ul>
    <li>";if($location){echo"<img src='../images/google_map.png' width='24px' border='0'> &nbsp;<font style='font-size:11px' color='blue'>$address</font></li>";}
          if($email){echo" <li><img src='../images/contact.png' width='24px' border='0'> &nbsp;<font style='font-size:11px' color=''> $email</font></li>";}
          if($phone){echo" <li><img src='../images/phone.png' border='0'> &nbsp;<font style='font-size:11px' color=''>(242)- $phone</font></li>";}
          if($url){echo"   <li><img src='../images/pc.png' width='24px' border='0'> &nbsp;<a class='cat1' href='http://$url'>$url</a></li>";}
        echo"</li>


    <li>
   ";if($facebook==True){echo" <a class='cat2' href='http://$facebook'style='left: 79%;position: relative;'><img src='../images/facebookpc.png' width='32' height='32' border='0'></a>";}
     if($twitter==True){echo"  <a class='cat2' href='http://$twitter' style='left: 80%;position: relative;'><img src='../images/twitterpc.png' width='32' height='32' border='0'></a>";}
    echo"</li>
</ul>

</div>
</div>";
}
else{
      echo"NOT Updated.";
    }
?>
<?php }else{echo"Please login to update information.";}?>