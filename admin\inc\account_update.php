<?php

  $user = $_SESSION['admin_id'];

  if($user)
  {
    if ($_POST['insert'])
    {
     //check fields
     $name        = $_POST['firstname'];
     $lastname    = $_POST['lastname'];
     $email       = $_POST['email'];

     $oldpassword = md5($_POST['oldpassword']);
     $newpassword = md5($_POST['newpassword']);
     $repeatnewpassword = md5($_POST['repeatnewpassword']);

     //connect db
     include'../function.php';

     $queryget = mysql_query("SELECT password FROM admin WHERE admin_id='$user'");
     $row = mysql_fetch_assoc($queryget);

     $oldpassworddb = $row['password'];

     if ($oldpassword==$oldpassworddb)
     {
     }
     else
       die("<div id='warning'>Incorrect password</div>");

     if ($newpassword==$repeatnewpassword)
     {
       $querychange = mysql_query("UPDATE admin SET password='$newpassword', firstname='$name', lastname='$lastname' WHERE admin_id='$user'");

      Echo'<div class="warning" style="padding:10px;">Information has been updated.</div>';
     }
      else
       $error = ('<div class="warning" style="padding:10px;">password doesn`t match</div>');
    }
  }
   else
       $success = ("you must me logged in before you can change your password");
?>
    <?
    if($error==true){echo$error;}
    if($success==true){echo$success;}
    ?>