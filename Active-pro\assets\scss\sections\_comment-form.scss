// main: ../main.scss
/*--------------------------------------------------------------
# Comment Form Section
--------------------------------------------------------------*/
.comment-form {
  padding-top: 10px;

  form {
    background-color: var(--surface-color);
    margin-top: 30px;
    padding: 30px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    h4 {
      font-weight: bold;
      font-size: 22px;
    }

    p {
      font-size: 14px;
    }

    input {
      background-color: var(--surface-color);
      color: var(--default-color);
      border: 1px solid color-mix(in srgb, var(--default-color), transparent 70%);
      font-size: 14px;
      border-radius: 4px;
      padding: 10px 10px;

      &:focus {
        color: var(--default-color);
        background-color: var(--surface-color);
        box-shadow: none;
        border-color: var(--accent-color);
      }

      &::placeholder {
        color: color-mix(in srgb, var(--default-color), transparent 50%);
      }
    }

    textarea {
      background-color: var(--surface-color);
      color: var(--default-color);
      border: 1px solid color-mix(in srgb, var(--default-color), transparent 70%);
      border-radius: 4px;
      padding: 10px 10px;
      font-size: 14px;
      height: 120px;

      &:focus {
        color: var(--default-color);
        box-shadow: none;
        border-color: var(--accent-color);
        background-color: var(--surface-color);
      }

      &::placeholder {
        color: color-mix(in srgb, var(--default-color), transparent 50%);
      }
    }

    .form-group {
      margin-bottom: 25px;
    }

    .btn-primary {
      border-radius: 4px;
      padding: 10px 20px;
      border: 0;
      background-color: var(--accent-color);
      color: var(--contrast-color);

      &:hover {
        color: var(--contrast-color);
        background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
      }
    }
  }
}