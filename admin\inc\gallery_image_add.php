<?
include'../function.php';

$date = date("Y-m-d H:i:s");


 if(isset($_FILES['upload'])== TRUE){
    $files = $_FILES['upload'];

    for($x = 0; $x< count($files['name']); $x++){
        $name     = $files['name'][$x];
        $tmp_name = $files['tmp_name'][$x];

        move_uploaded_file($tmp_name, '../images/gallery/'. $name);

      $insert = mysql_query("INSERT INTO images VALUES('','$name','$date')");

$complete='<div class="warning" style="padding:20px; text-align:center; font-size:12px;">Your information has been updated successfully.</div>';

   }
  }

?>
<?
     if($insert==true){Echo $complete; Echo $error;}
?>
<form action='' method='POST' class='horizontal-form' enctype='multipart/form-data'>
<section>
  <h1>Upload Images</h1>
  <h2>Upload as may images to attract lots of viewers.</h2>

             <p>
                <label for="id_website_url">Property Image:</label>
                <span><input type="file" name="upload[]" multiple></span>
            </p>
</section>
 <section class="flow-navigation-bottom flow-navigation">

            <input class="button" name="insert" type="submit" value="Save changes">

 </section>
 <section></section>
 </form>