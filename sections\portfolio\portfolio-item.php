<?php
// Read portfolio data from JSON file
$json_file = __DIR__ . '/portfolio-data.json';
$portfolio_data = [];

if (file_exists($json_file)) {
    $json_content = file_get_contents($json_file);
    $data = json_decode($json_content, true);
    if ($data && isset($data['portfolio_items'])) {
        $portfolio_data = $data['portfolio_items'];
    }
}

// Generate portfolio items dynamically
foreach ($portfolio_data as $item) {
    // Skip items with empty names (unless they have descriptions)
    if (empty($item['name']) && empty($item['description'])) {
        continue;
    }

    echo '<div class="col-lg-4 col-md-6 portfolio-item ' . htmlspecialchars($item['filter']) . '">';
    echo '  <div class="portfolio-wrap">';
    echo '    <img src="' . htmlspecialchars($item['image']) . '" class="img-fluid" alt="' . htmlspecialchars($item['alt']) . '">';
    echo '    <div class="portfolio-info">';
    echo '      <h4>' . htmlspecialchars($item['name']) . '</h4>';
    echo '      <p>' . htmlspecialchars($item['description']) . '</p>';
    echo '      <div class="portfolio-links">';
    echo '        <a href="' . htmlspecialchars($item['gallery_link']) . '" data-gall="portfolioGallery" class="venobox" title="' . htmlspecialchars($item['gallery_title']) . '"><i class="bx bx-plus"></i></a>';
    echo '        <a href="' . htmlspecialchars($item['details_link']) . '" title="More Details"><i class="bx bx-link"></i></a>';
    echo '      </div>';
    echo '    </div>';
    echo '  </div>';
    echo '</div>';
    echo "\n";
}
?>
