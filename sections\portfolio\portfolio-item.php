<?php
// Read portfolio data from JSON file
$json_file = __DIR__ . '/portfolio-data.json';
$portfolio_data = [];

if (file_exists($json_file)) {
    $json_content = file_get_contents($json_file);
    $data = json_decode($json_content, true);
    if ($data && isset($data['portfolio_items'])) {
        $portfolio_data = $data['portfolio_items'];
    }
}

// Filter out items with empty names and descriptions
$filtered_data = array_filter($portfolio_data, function($item) {
    return !empty($item['name']) || !empty($item['description']);
});

$item_count = count($filtered_data);
$total_items = $item_count * 2; // Duplicated for infinite loop

// Ensure minimum items for smooth animation
if ($item_count < 3) {
    // If less than 3 items, duplicate more times to ensure smooth scrolling
    $total_items = $item_count * 4;
    $animation_move = $item_count * 2;
} else {
    $animation_move = $item_count;
}

// Generate dynamic CSS for perfect loop calculations
echo '<style>';
echo '/* Dynamic slideshow calculations for ' . $item_count . ' items */';
echo '.slideshow-track { width: calc(300px * ' . $total_items . ') !important; }';
echo '@keyframes slide { 0% { transform: translateX(0); } 100% { transform: translateX(calc(-300px * ' . $animation_move . ')); } }';
echo '@media (max-width: 768px) {';
echo '  .slideshow-track { width: calc(240px * ' . $total_items . ') !important; }';
echo '  @keyframes slide { 0% { transform: translateX(0); } 100% { transform: translateX(calc(-240px * ' . $animation_move . ')); } }';
echo '}';
echo '@media (max-width: 480px) {';
echo '  .slideshow-track { width: calc(200px * ' . $total_items . ') !important; }';
echo '  @keyframes slide { 0% { transform: translateX(0); } 100% { transform: translateX(calc(-200px * ' . $animation_move . ')); } }';
echo '}';
echo '</style>';

// Create the slideshow track with duplicated items for infinite loop
echo '<div class="slideshow-container">';
echo '  <div class="slideshow-track">';

// First set of items
foreach ($filtered_data as $item) {
    $category = str_replace('filter-', '', $item['filter']);
    echo '    <div class="slideshow-item ' . htmlspecialchars($item['filter']) . '">';
    echo '      <img src="' . htmlspecialchars($item['image']) . '" alt="' . htmlspecialchars($item['alt']) . '">';
    echo '      <div class="category-badge">' . ucfirst($category) . '</div>';
    echo '      <div class="slideshow-item-info">';
    echo '        <h4>' . htmlspecialchars($item['name']) . '</h4>';
    echo '        <p>' . htmlspecialchars($item['description']) . '</p>';
    echo '        <div class="portfolio-links">';
    echo '          <a href="' . htmlspecialchars($item['gallery_link']) . '" data-gall="portfolioGallery" class="venobox" title="' . htmlspecialchars($item['gallery_title']) . '"><i class="bx bx-plus"></i></a>';
    echo '          <a href="' . htmlspecialchars($item['details_link']) . '" title="More Details"><i class="bx bx-link"></i></a>';
    echo '        </div>';
    echo '      </div>';
    echo '    </div>';
}

// Duplicate the items for seamless infinite loop
$duplications_needed = ($item_count < 3) ? 3 : 1; // More duplications for fewer items

for ($d = 0; $d < $duplications_needed; $d++) {
    foreach ($filtered_data as $item) {
        $category = str_replace('filter-', '', $item['filter']);
        echo '    <div class="slideshow-item ' . htmlspecialchars($item['filter']) . '">';
        echo '      <img src="' . htmlspecialchars($item['image']) . '" alt="' . htmlspecialchars($item['alt']) . '">';
        echo '      <div class="category-badge">' . ucfirst($category) . '</div>';
        echo '      <div class="slideshow-item-info">';
        echo '        <h4>' . htmlspecialchars($item['name']) . '</h4>';
        echo '        <p>' . htmlspecialchars($item['description']) . '</p>';
        echo '        <div class="portfolio-links">';
        echo '          <a href="' . htmlspecialchars($item['gallery_link']) . '" data-gall="portfolioGallery" class="venobox" title="' . htmlspecialchars($item['gallery_title']) . '"><i class="bx bx-plus"></i></a>';
        echo '          <a href="' . htmlspecialchars($item['details_link']) . '" title="More Details"><i class="bx bx-link"></i></a>';
        echo '        </div>';
        echo '      </div>';
        echo '    </div>';
    }
}

echo '  </div>';
echo '</div>';
?>
