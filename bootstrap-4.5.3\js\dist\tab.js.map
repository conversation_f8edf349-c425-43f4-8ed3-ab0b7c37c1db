{"version": 3, "file": "tab.js", "sources": ["../src/tab.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = '> li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = '> .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if (this._element.parentNode &&\n        this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n        $(this._element).hasClass(CLASS_NAME_ACTIVE) ||\n        $(this._element).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let target\n    let previous\n    const listElement = $(this._element).closest(SELECTOR_NAV_LIST_GROUP)[0]\n    const selector = Util.getSelectorFromElement(this._element)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = $.makeArray($(listElement).find(itemSelector))\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE, {\n      relatedTarget: this._element\n    })\n\n    const showEvent = $.Event(EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (previous) {\n      $(previous).trigger(hideEvent)\n    }\n\n    $(this._element).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented() ||\n        hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (selector) {\n      target = document.querySelector(selector)\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      const hiddenEvent = $.Event(EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n\n      const shownEvent = $.Event(EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n\n      $(previous).trigger(hiddenEvent)\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      $(container).find(SELECTOR_ACTIVE_UL) :\n      $(container).children(SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && $(active).hasClass(CLASS_NAME_FADE))\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = Util.getTransitionDurationFromElement(active)\n\n      $(active)\n        .removeClass(CLASS_NAME_SHOW)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      $(active).removeClass(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = $(active.parentNode).find(\n        SELECTOR_DROPDOWN_ACTIVE_CHILD\n      )[0]\n\n      if (dropdownChild) {\n        $(dropdownChild).removeClass(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    $(element).addClass(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    Util.reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && $(element.parentNode).hasClass(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = $(element).closest(SELECTOR_DROPDOWN)[0]\n\n      if (dropdownElement) {\n        const dropdownToggleList = [].slice.call(dropdownElement.querySelectorAll(SELECTOR_DROPDOWN_TOGGLE))\n\n        $(dropdownToggleList).addClass(CLASS_NAME_ACTIVE)\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $this = $(this)\n      let data = $this.data(DATA_KEY)\n\n      if (!data) {\n        data = new Tab(this)\n        $this.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    Tab._jQueryInterface.call($(this), 'show')\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Tab._jQueryInterface\n$.fn[NAME].Constructor = Tab\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tab._jQueryInterface\n}\n\nexport default Tab\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "CLASS_NAME_DROPDOWN_MENU", "CLASS_NAME_ACTIVE", "CLASS_NAME_DISABLED", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_DROPDOWN", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_UL", "SELECTOR_DATA_TOGGLE", "SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "element", "_element", "show", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "hasClass", "target", "previous", "listElement", "closest", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "itemSelector", "nodeName", "makeArray", "find", "length", "hideEvent", "Event", "relatedTarget", "showEvent", "trigger", "isDefaultPrevented", "document", "querySelector", "_activate", "complete", "hiddenEvent", "shownEvent", "dispose", "removeData", "container", "callback", "activeElements", "children", "active", "isTransitioning", "_transitionComplete", "transitionDuration", "getTransitionDurationFromElement", "removeClass", "one", "TRANSITION_END", "emulateTransitionEnd", "dropdown<PERSON><PERSON>d", "getAttribute", "setAttribute", "addClass", "reflow", "classList", "contains", "add", "dropdownElement", "dropdownToggleList", "slice", "call", "querySelectorAll", "_jQueryInterface", "config", "each", "$this", "data", "TypeError", "on", "event", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAG,KAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,QAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,UAAU,YAAUL,SAA1B;EACA,IAAMM,YAAY,cAAYN,SAA9B;EACA,IAAMO,UAAU,YAAUP,SAA1B;EACA,IAAMQ,WAAW,aAAWR,SAA5B;EACA,IAAMS,oBAAoB,aAAWT,SAAX,GAAuBC,YAAjD;EAEA,IAAMS,wBAAwB,GAAG,eAAjC;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EAEA,IAAMC,iBAAiB,GAAG,WAA1B;EACA,IAAMC,uBAAuB,GAAG,mBAAhC;EACA,IAAMC,eAAe,GAAG,SAAxB;EACA,IAAMC,kBAAkB,GAAG,gBAA3B;EACA,IAAMC,oBAAoB,GAAG,iEAA7B;EACA,IAAMC,wBAAwB,GAAG,kBAAjC;EACA,IAAMC,8BAA8B,GAAG,0BAAvC;EAEA;;;;;;MAMMC;EACJ,eAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACD;;;;;EAQD;WAEAE,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKD,QAAL,CAAcE,UAAd,IACA,KAAKF,QAAL,CAAcE,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAD3C,IAEA1B,qBAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBM,QAAjB,CAA0BnB,iBAA1B,CAFA,IAGAR,qBAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBM,QAAjB,CAA0BlB,mBAA1B,CAHJ,EAGoD;EAClD;EACD;;EAED,QAAImB,MAAJ;EACA,QAAIC,QAAJ;EACA,QAAMC,WAAW,GAAG9B,qBAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBU,OAAjB,CAAyBlB,uBAAzB,EAAkD,CAAlD,CAApB;EACA,QAAMmB,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4B,KAAKb,QAAjC,CAAjB;;EAEA,QAAIS,WAAJ,EAAiB;EACf,UAAMK,YAAY,GAAGL,WAAW,CAACM,QAAZ,KAAyB,IAAzB,IAAiCN,WAAW,CAACM,QAAZ,KAAyB,IAA1D,GAAiErB,kBAAjE,GAAsFD,eAA3G;EACAe,MAAAA,QAAQ,GAAG7B,qBAAC,CAACqC,SAAF,CAAYrC,qBAAC,CAAC8B,WAAD,CAAD,CAAeQ,IAAf,CAAoBH,YAApB,CAAZ,CAAX;EACAN,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACU,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,QAAMC,SAAS,GAAGxC,qBAAC,CAACyC,KAAF,CAAQvC,UAAR,EAAoB;EACpCwC,MAAAA,aAAa,EAAE,KAAKrB;EADgB,KAApB,CAAlB;EAIA,QAAMsB,SAAS,GAAG3C,qBAAC,CAACyC,KAAF,CAAQrC,UAAR,EAAoB;EACpCsC,MAAAA,aAAa,EAAEb;EADqB,KAApB,CAAlB;;EAIA,QAAIA,QAAJ,EAAc;EACZ7B,MAAAA,qBAAC,CAAC6B,QAAD,CAAD,CAAYe,OAAZ,CAAoBJ,SAApB;EACD;;EAEDxC,IAAAA,qBAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBuB,OAAjB,CAAyBD,SAAzB;;EAEA,QAAIA,SAAS,CAACE,kBAAV,MACAL,SAAS,CAACK,kBAAV,EADJ,EACoC;EAClC;EACD;;EAED,QAAIb,QAAJ,EAAc;EACZJ,MAAAA,MAAM,GAAGkB,QAAQ,CAACC,aAAT,CAAuBf,QAAvB,CAAT;EACD;;EAED,SAAKgB,SAAL,CACE,KAAK3B,QADP,EAEES,WAFF;;EAKA,QAAMmB,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAMC,WAAW,GAAGlD,qBAAC,CAACyC,KAAF,CAAQtC,YAAR,EAAsB;EACxCuC,QAAAA,aAAa,EAAE,KAAI,CAACrB;EADoB,OAAtB,CAApB;EAIA,UAAM8B,UAAU,GAAGnD,qBAAC,CAACyC,KAAF,CAAQpC,WAAR,EAAqB;EACtCqC,QAAAA,aAAa,EAAEb;EADuB,OAArB,CAAnB;EAIA7B,MAAAA,qBAAC,CAAC6B,QAAD,CAAD,CAAYe,OAAZ,CAAoBM,WAApB;EACAlD,MAAAA,qBAAC,CAAC,KAAI,CAACqB,QAAN,CAAD,CAAiBuB,OAAjB,CAAyBO,UAAzB;EACD,KAXD;;EAaA,QAAIvB,MAAJ,EAAY;EACV,WAAKoB,SAAL,CAAepB,MAAf,EAAuBA,MAAM,CAACL,UAA9B,EAA0C0B,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF;;WAEDG,UAAA,mBAAU;EACRpD,IAAAA,qBAAC,CAACqD,UAAF,CAAa,KAAKhC,QAAlB,EAA4BzB,QAA5B;EACA,SAAKyB,QAAL,GAAgB,IAAhB;EACD;;;WAID2B,YAAA,mBAAU5B,OAAV,EAAmBkC,SAAnB,EAA8BC,QAA9B,EAAwC;EAAA;;EACtC,QAAMC,cAAc,GAAGF,SAAS,KAAKA,SAAS,CAAClB,QAAV,KAAuB,IAAvB,IAA+BkB,SAAS,CAAClB,QAAV,KAAuB,IAA3D,CAAT,GACrBpC,qBAAC,CAACsD,SAAD,CAAD,CAAahB,IAAb,CAAkBvB,kBAAlB,CADqB,GAErBf,qBAAC,CAACsD,SAAD,CAAD,CAAaG,QAAb,CAAsB3C,eAAtB,CAFF;EAIA,QAAM4C,MAAM,GAAGF,cAAc,CAAC,CAAD,CAA7B;EACA,QAAMG,eAAe,GAAGJ,QAAQ,IAAKG,MAAM,IAAI1D,qBAAC,CAAC0D,MAAD,CAAD,CAAU/B,QAAV,CAAmBjB,eAAnB,CAA/C;;EACA,QAAMuC,QAAQ,GAAG,SAAXA,QAAW;EAAA,aAAM,MAAI,CAACW,mBAAL,CACrBxC,OADqB,EAErBsC,MAFqB,EAGrBH,QAHqB,CAAN;EAAA,KAAjB;;EAMA,QAAIG,MAAM,IAAIC,eAAd,EAA+B;EAC7B,UAAME,kBAAkB,GAAG5B,wBAAI,CAAC6B,gCAAL,CAAsCJ,MAAtC,CAA3B;EAEA1D,MAAAA,qBAAC,CAAC0D,MAAD,CAAD,CACGK,WADH,CACepD,eADf,EAEGqD,GAFH,CAEO/B,wBAAI,CAACgC,cAFZ,EAE4BhB,QAF5B,EAGGiB,oBAHH,CAGwBL,kBAHxB;EAID,KAPD,MAOO;EACLZ,MAAAA,QAAQ;EACT;EACF;;WAEDW,sBAAA,6BAAoBxC,OAApB,EAA6BsC,MAA7B,EAAqCH,QAArC,EAA+C;EAC7C,QAAIG,MAAJ,EAAY;EACV1D,MAAAA,qBAAC,CAAC0D,MAAD,CAAD,CAAUK,WAAV,CAAsBvD,iBAAtB;EAEA,UAAM2D,aAAa,GAAGnE,qBAAC,CAAC0D,MAAM,CAACnC,UAAR,CAAD,CAAqBe,IAArB,CACpBpB,8BADoB,EAEpB,CAFoB,CAAtB;;EAIA,UAAIiD,aAAJ,EAAmB;EACjBnE,QAAAA,qBAAC,CAACmE,aAAD,CAAD,CAAiBJ,WAAjB,CAA6BvD,iBAA7B;EACD;;EAED,UAAIkD,MAAM,CAACU,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzCV,QAAAA,MAAM,CAACW,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDrE,IAAAA,qBAAC,CAACoB,OAAD,CAAD,CAAWkD,QAAX,CAAoB9D,iBAApB;;EACA,QAAIY,OAAO,CAACgD,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1ChD,MAAAA,OAAO,CAACiD,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAEDpC,IAAAA,wBAAI,CAACsC,MAAL,CAAYnD,OAAZ;;EAEA,QAAIA,OAAO,CAACoD,SAAR,CAAkBC,QAAlB,CAA2B/D,eAA3B,CAAJ,EAAiD;EAC/CU,MAAAA,OAAO,CAACoD,SAAR,CAAkBE,GAAlB,CAAsB/D,eAAtB;EACD;;EAED,QAAIS,OAAO,CAACG,UAAR,IAAsBvB,qBAAC,CAACoB,OAAO,CAACG,UAAT,CAAD,CAAsBI,QAAtB,CAA+BpB,wBAA/B,CAA1B,EAAoF;EAClF,UAAMoE,eAAe,GAAG3E,qBAAC,CAACoB,OAAD,CAAD,CAAWW,OAAX,CAAmBnB,iBAAnB,EAAsC,CAAtC,CAAxB;;EAEA,UAAI+D,eAAJ,EAAqB;EACnB,YAAMC,kBAAkB,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcH,eAAe,CAACI,gBAAhB,CAAiC9D,wBAAjC,CAAd,CAA3B;EAEAjB,QAAAA,qBAAC,CAAC4E,kBAAD,CAAD,CAAsBN,QAAtB,CAA+B9D,iBAA/B;EACD;;EAEDY,MAAAA,OAAO,CAACiD,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAId,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF;;;QAIMyB,mBAAP,0BAAwBC,MAAxB,EAAgC;EAC9B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAMC,KAAK,GAAGnF,qBAAC,CAAC,IAAD,CAAf;EACA,UAAIoF,IAAI,GAAGD,KAAK,CAACC,IAAN,CAAWxF,QAAX,CAAX;;EAEA,UAAI,CAACwF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIjE,GAAJ,CAAQ,IAAR,CAAP;EACAgE,QAAAA,KAAK,CAACC,IAAN,CAAWxF,QAAX,EAAqBwF,IAArB;EACD;;EAED,UAAI,OAAOH,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOG,IAAI,CAACH,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAII,SAAJ,wBAAkCJ,MAAlC,QAAN;EACD;;EAEDG,QAAAA,IAAI,CAACH,MAAD,CAAJ;EACD;EACF,KAhBM,CAAP;EAiBD;;;;0BA1KoB;EACnB,aAAOtF,OAAP;EACD;;;;;EA2KH;;;;;;;AAMAK,uBAAC,CAAC8C,QAAD,CAAD,CACGwC,EADH,CACMhF,oBADN,EAC4BU,oBAD5B,EACkD,UAAUuE,KAAV,EAAiB;EAC/DA,EAAAA,KAAK,CAACC,cAAN;;EACArE,EAAAA,GAAG,CAAC6D,gBAAJ,CAAqBF,IAArB,CAA0B9E,qBAAC,CAAC,IAAD,CAA3B,EAAmC,MAAnC;EACD,CAJH;EAMA;;;;;;AAMAA,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAayB,GAAG,CAAC6D,gBAAjB;AACAhF,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAW+F,WAAX,GAAyBtE,GAAzB;;AACAnB,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWgG,UAAX,GAAwB,YAAM;EAC5B1F,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOoB,GAAG,CAAC6D,gBAAX;EACD,CAHD;;;;;;;;"}