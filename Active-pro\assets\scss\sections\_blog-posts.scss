// main: ../main.scss
/*--------------------------------------------------------------
# Blog Posts Section
--------------------------------------------------------------*/
.blog-posts {
  .title-wrap {
    padding-bottom: 30px;
  }

  .content-subtitle {
    font-size: 15px;
    margin-bottom: 10px;
    display: block;
    color: var(--default-color);
  }

  .content-title {
    color: var(--heading-color);
    font-size: 22px;
    margin-bottom: 30px;
  }

  .post-entry {
    .thumb {
      margin-bottom: 20px;

      img {
        transition: 0.3s all ease;
      }

      &:hover {
        img {
          opacity: 0.8;
        }
      }
    }

    .meta {
      font-size: 12px;
      margin-bottom: 20px;

      .cat {
        text-transform: uppercase;
        font-weight: normal;
        color: var(--heading-color);
      }

      .date {
        color: color-mix(in srgb, var(--default-color), transparent 25%);
      }
    }

    .post-content {
      padding-left: 30px;
      padding-right: 30px;

      h3 {
        font-size: 18px;
        line-height: 1.2;

        a {
          color: var(--heading-color);

          &:hover {
            color: var(--accent-color);
          }
        }
      }
    }
  }

  .author {
    .pic {
      flex: 0 0 50px;
      margin-right: 20px;
    }

    .author-name {
      line-height: 1.3;

      strong {
        color: var(--heading-color);
        font-weight: normal;
      }

      span {
        font-size: 14px;
        color: color-mix(in srgb, var(--default-color), transparent 25%);
      }
    }
  }
}