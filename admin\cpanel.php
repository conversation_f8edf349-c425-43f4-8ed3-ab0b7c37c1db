<?PHP
session_start();

        $user = $_SESSION['admin_id'];

          //connect to the data base
        include'./function.php';

          //query
         $get = mysql_query("SELECT * FROM admin WHERE admin_id='$user' and activated =1");

         while($row = mysql_fetch_assoc($get))

         {
            $admin     = $row['admin_id'];
            $lastname  = $row['lastname'];
            $level     = $row['level'];
            $firstname = $row['firstname'];
            $date      = $row['date_last_access'];
            $pimg      = $row['image_name'];
         }

        if($admin==false)
          {
            header("location: ./t2.php");
          }
  else{
	 
  ?>
<!DOCTYPE html>
<html lang="en">
<head>

	<meta charset="utf-8">
       <title>Control Panel | Administrative Section </title>

     <link rel="stylesheet" type="text/css" href="../cpanel/cpanel.css"/>
     <link rel="stylesheet" type="text/css" href="../cpanel/new_style.css"/>
     <link rel="shortcut icon" href="..images/cpanel/icon_core_homepage2.jpg">
     <link href="http://thebigappleacademy.comckeditor/_samples/sample.css" rel="stylesheet" type="text/css" />

       <script type="text/javascript" src="http://thebigappleacademy.com/ckeditor/ckeditor.js"></script>
       <script src="http://thebigappleacademy.comckeditor/_samples/sample.js" type="text/javascript"></script>
       <script type="text/javascript" src="./jscookmenu.js"></script>

     

</head>
<body>

<header id="menu">

    <div id="wrappers">

    <div id="login">
             <ul>

<?
if($user = $_SESSION['admin_id']==TRUE)

  {
  echo'<ul>
	  <li>
	  	<a href="/logout.php"><img src="../images/Log Out.png"  alt="logout">Sign out</a>
	  </li>
	  <li>
	  	<a href="cpanel.php"><img src="../cpanel/images/chome.png">Home</a>
	  </li>
  </ul>';
  }
  ?>
             </ul>
          </div>


    </div>

</header>

<section id="main-panel">

<div id="WorkAreas">
<?
	
$page = $_GET['p'];
$section = $_GET['section'];
 if($page)
{
    if(!strpos($page,".")&&!strpos($page,"/"))
    {
      //check for section
        if (!$section)
        $section = "inc";

             $path = $section."/".$page.".php";
             if(file_exists($path))
                {
                }
               else
                {
                echo "Sorry, that page doesn't exist";
                }
    } else
          echo"Not Allowed";

               include($path);
}
else
{
include'./inc/content_management.php';

}

;?>
 </div>
 </section>

 
<footer>
        <?php include'../design/footer-admin.php';?>
</footer>

</body>
</html>
<?php }?>