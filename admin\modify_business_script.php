<?php
session_start();
include'../design/header_profile.php';
 include'../function.php';
  $id = $_GET['id'];
  $user = $_SESSION['username'];
   $get = mysql_query("SELECT * FROM user WHERE username='$user'");
        while($run = mysql_fetch_assoc($get))
        {
        $user_id = $run['id'];
        }

$submit = $_POST['insert'];

$email            = strip_tags($_POST['email']);
$phone            = strip_tags($_POST['phone']);
$company          = strip_tags($_POST['company']);
$website          = strip_tags($_POST['website']);
$keywords         = strip_tags($_POST['keywords']);
$location          = strip_tags($_POST['location']);
$facebook         = $_POST['facebook'];
$twitter          = $_POST['twitter'];
$description      = strip_tags($_POST['description']);

$imagename             = $_FILES['myfile']['name'];
$tmp_name         = $_FILES['myfile']['tmp_name'];

if($submit)
{
// conncet to the database
        include'../function.php';

        //who own this business

         // Start Upload process
         $image = "../image/$imagename";

         move_uploaded_file($tmp_name,$image);

        //insert data
        $insert = mysql_query("UPDATE employer_mail SET  user_id      ='$user_id',
                                                         email        ='$email',
                                                         phone        ='$phone',
                                                         location     ='$address',
                                                         company      ='$company',
                                                         website      ='$website',
                                                         image        ='$image',
                                                         keywords     ='$keywords',
                                                         facebook     ='$facebook',
                                                         twitter      ='$twitter',
                                                         description  ='$description'


                                                         WHERE  id       = '$id'");

echo"

<div class='resultItem resultItemMouseOver resultItemOff'>
				<div class='addImage'>

<a href='http://$url'>";
if(($image=="")||($image=="image/")||($image=="../image/"))
{echo"<img border='0' src='../image/EBS/explorebsicon.png' width='110'>";}
else
{echo"<img border='0' src='../$image' width='110'>";}echo"
</a>
</div>
<div class='addTekst'>
<div class='starRatingSearch star0'><font style='font-size:14px' face='arial,sans-serif' color='blue'><a>$title</a></font></div>

<br clear='left'>
";

if($description==True){echo"
<font style='font-size:12px' face='arial,sans-serif' color='red'><a class='cat'>";echo substr("$description" ,0,250); echo"...</a></font>
";}echo"

<ul>
    <li>";if($location){echo"<img src='../images/google_map.png' width='24px' border='0'> &nbsp;<font style='font-size:11px' color='blue'>$location</font></li>";}
          if($email){echo" <li><img src='../images/contact.png' width='24px' border='0'> &nbsp;<font style='font-size:11px' color=''> $email</font></li>";}
          if($phone){echo" <li><img src='../images/phone.png' border='0'> &nbsp;<font style='font-size:11px' color=''>(242)- $phone</font></li>";}
          if($url){echo"   <li><img src='../images/pc.png' width='24px' border='0'> &nbsp;<a class='cat1' href='http://$url'>$url</a></li>";}
        echo"</li>


    <li>
   ";if($facebook==True){echo" <a class='cat2' href='http://$facebook'style='left: 79%;position: relative;'><img src='../images/facebookpc.png' width='32' height='32' border='0'></a>";}
     if($twitter==True){echo"  <a class='cat2' href='http://$twitter' style='left: 80%;position: relative;'><img src='../images/twitterpc.png' width='32' height='32' border='0'></a>";}
    echo"</li>
</ul>

</div>
</div>";
}
else{
      echo"Not Updated.";
    }

?>