<?php session_start(); $admin = $_SESSION['admin_id'];
if($_SESSION['admin_id']==true)
{
  header("location: cpanel.php");
          exit();
}
?>
<?php

include '../function2.php';

 if(isAdmin($session_admin))
     {
     header("location: cpanel.php");
     exit();
     }

if($_POST['login'])
{
   $email = $_POST['admin_id'];
   $password = $_POST['password'];
   $rememberme = $_POST['rememberme'];


   if ($email&&$password)
       {
       $login = mysql_query("SELECT * FROM admin WHERE admin_id='$email'");

       while($row = mysql_fetch_assoc($login))


       {
            $db_password = $row['password'];
             $activated = $row['activated'];
             if ($activated=='0')
             {

             echo"your account is not activated, please check your email";
             exit();
             }

         if (md5($password)==$db_password)

             $loginok = True;
         else
              $loginok = FALSE;

          if  ($loginok==True)
             {
          if ($rememberme=="on")
          setcookie("admin_id", $email, time()+7200);
          else if ($rememberme=="")
          $_SESSION['admin_id']=$email;

          $date = date("Y-m-d, h:i:s");
          $ip   = $_SERVER['REMOTE_ADDR'];

$update = mysql_query("UPDATE admin SET date_last_access='$date', ipaddress='$ip' WHERE admin_id='$email'");


          header("location: cpanel.php");
          exit();
              }
       }
     $error ="<div class='warning' align='center'>
               Incorrect email address or an password.
               </div>";
      }

   else{

     $error ="<div class='warning' align='center'>Please enter a email address and password.</div>";
      }
}

?>
<!DOCTYPE html">
<html lang="eng">
     <head>
     <title>Control Panel</title>
      <link rel="stylesheet" type="text/css" href="./cpanel.css"/>

      <script src="jquery.js" type="text/javascript"></script>

      <link rel="shortcut icon" href="./js/images/banner.ico">
      <script src="../prettyphoto/js/jquery-1.4.4.min.js" type="text/javascript"></script>
      <!--script src="../prettyphoto/js/jquery.lint.js" type="text/javascript" charset="utf-8"></script-->
      <link rel="stylesheet" href="./prettyphoto/css/prettyPhoto.css" type="text/css" media="screen" title="prettyPhoto main stylesheet" charset="utf-8" />
      <script src="../prettyphoto/js/jquery.prettyPhoto.js" type="text/javascript" charset="utf-8"></script>

      <script type="text/javascript" src="./jscookmenu.js"></script>
<meta charset="UTF-8">
     </head>
     <body>

<div id="wrapper">

 <div id="client" align="right">

        <?php if($_SESSION['admin_id']==True){echo"Welcome,<b>".$_SESSION['admin_id']."</b>";}?>

        </div>

          <div id="bv_Image1" style="overflow:hidden;position:absolute;left:434px;top:20px;z-index:0" align="left">
            <img src="../images/tool.png" id="Image1" alt="" align="top" border="0" style="width:126px;height:133px;">
          </div>

        <div id="bv_Image2" style="overflow:hidden;position:absolute;left:0px;top:20px;z-index:1" align="left">
           <img src="../images/01733.png" id="Image2" alt="" align="top" border="0" style="width:482px;height:98px;">
        </div>


        <div style="position: relative; top: 152px; padding-bottom:150px;" id="container">

        <form id="Form1" action="" method="POST" enctype="application/x-www-form-urlencoded">


        <div id="loginarea">

                    <div id="form">
                     <?if($error==true){echo$error;}?>
                    <h3 style="border-bottom: 1px dotted lightgray;">Administration</h3>

                     <p> Enter a valid email address and password in the spaces provided, then hit the submit button to login </p>

                      <? echo'Username: <input id="Username" name="admin_id" placeholder="Username" value="'.$email.'" type="text">';?>
                      <br/><br/>

                      Password: <input id="Password" name="password" placeholder="Password" value="" type="password">
                      <br/> <br/>

                      <input type="submit" class="button" name="login" value="&nbsp;Login&nbsp;"  />

                    </div>
                 </form>
           </div>

          </div>

</div>
        <?php include'../design/footer-admin.php';?>
        </body>


</html>
