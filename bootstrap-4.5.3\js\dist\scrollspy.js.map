{"version": 3, "file": "scrollspy.js", "sources": ["../src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_ITEMS = '.dropdown-item'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} ${SELECTOR_DROPDOWN_ITEMS}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    $(this._scrollElement).on(EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET : METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod : this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() : 0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = [].slice.call(document.querySelectorAll(this._selector))\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = Util.getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = document.querySelector(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            // TODO (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._scrollElement).off(EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && Util.isElement(config.target)) {\n      let id = $(config.target).attr('id')\n      if (!id) {\n        id = Util.getUID(NAME)\n        $(config.target).attr('id', id)\n      }\n\n      config.target = `#${id}`\n    }\n\n    Util.typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight : this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector\n      .split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const $link = $([].slice.call(document.querySelectorAll(queries.join(','))))\n\n    if ($link.hasClass(CLASS_NAME_DROPDOWN_ITEM)) {\n      $link.closest(SELECTOR_DROPDOWN)\n        .find(SELECTOR_DROPDOWN_TOGGLE)\n        .addClass(CLASS_NAME_ACTIVE)\n      $link.addClass(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      $link.addClass(CLASS_NAME_ACTIVE)\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(`${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n        .addClass(CLASS_NAME_ACTIVE)\n      // Handle special case when .nav-link is inside .nav-item\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(SELECTOR_NAV_ITEMS)\n        .children(SELECTOR_NAV_LINKS)\n        .addClass(CLASS_NAME_ACTIVE)\n    }\n\n    $(this._scrollElement).trigger(EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    [].slice.call(document.querySelectorAll(this._selector))\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const scrollSpys = [].slice.call(document.querySelectorAll(SELECTOR_DATA_SPY))\n  const scrollSpysLength = scrollSpys.length\n\n  for (let i = scrollSpysLength; i--;) {\n    const $spy = $(scrollSpys[i])\n    ScrollSpy._jQueryInterface.call($spy, $spy.data())\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = ScrollSpy._jQueryInterface\n$.fn[NAME].Constructor = ScrollSpy\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return ScrollSpy._jQueryInterface\n}\n\nexport default ScrollSpy\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "<PERSON><PERSON><PERSON>", "offset", "method", "target", "DefaultType", "EVENT_ACTIVATE", "EVENT_SCROLL", "EVENT_LOAD_DATA_API", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_ITEMS", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "element", "config", "_element", "_scrollElement", "tagName", "window", "_config", "_getConfig", "_selector", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "on", "event", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "slice", "call", "document", "querySelectorAll", "map", "targetSelector", "<PERSON><PERSON>", "getSelectorFromElement", "querySelector", "targetBCR", "getBoundingClientRect", "width", "height", "top", "filter", "item", "sort", "a", "b", "for<PERSON>ach", "push", "dispose", "removeData", "off", "isElement", "id", "attr", "getUID", "typeCheckConfig", "pageYOffset", "scrollTop", "scrollHeight", "Math", "max", "body", "documentElement", "_getOffsetHeight", "innerHeight", "maxScroll", "length", "_activate", "_clear", "i", "isActiveTarget", "queries", "split", "selector", "$link", "join", "hasClass", "closest", "find", "addClass", "parents", "prev", "children", "trigger", "relatedTarget", "node", "classList", "contains", "remove", "_jQueryInterface", "each", "data", "TypeError", "scrollSpys", "scrollSpysLength", "$spy", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAG,WAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,cAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,EADM;EAEdC,EAAAA,MAAM,EAAE,MAFM;EAGdC,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,IAAMC,WAAW,GAAG;EAClBH,EAAAA,MAAM,EAAE,QADU;EAElBC,EAAAA,MAAM,EAAE,QAFU;EAGlBC,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,IAAME,cAAc,gBAAcV,SAAlC;EACA,IAAMW,YAAY,cAAYX,SAA9B;EACA,IAAMY,mBAAmB,YAAUZ,SAAV,GAAsBC,YAA/C;EAEA,IAAMY,wBAAwB,GAAG,eAAjC;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EAEA,IAAMC,iBAAiB,GAAG,qBAA1B;EACA,IAAMC,uBAAuB,GAAG,mBAAhC;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,mBAAmB,GAAG,kBAA5B;EACA,IAAMC,iBAAiB,GAAG,WAA1B;EACA,IAAMC,uBAAuB,GAAG,gBAAhC;EACA,IAAMC,wBAAwB,GAAG,kBAAjC;EAEA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,eAAe,GAAG,UAAxB;EAEA;;;;;;MAMMC;EACJ,qBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAAA;;EAC3B,SAAKC,QAAL,GAAgBF,OAAhB;EACA,SAAKG,cAAL,GAAsBH,OAAO,CAACI,OAAR,KAAoB,MAApB,GAA6BC,MAA7B,GAAsCL,OAA5D;EACA,SAAKM,OAAL,GAAe,KAAKC,UAAL,CAAgBN,MAAhB,CAAf;EACA,SAAKO,SAAL,GAAoB,KAAKF,OAAL,CAAaxB,MAAhB,SAA0BS,kBAA1B,UACQ,KAAKe,OAAL,CAAaxB,MADrB,SAC+BW,mBAD/B,WAEQ,KAAKa,OAAL,CAAaxB,MAFrB,SAE+Ba,uBAF/B,CAAjB;EAGA,SAAKc,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEAnC,IAAAA,qBAAC,CAAC,KAAK0B,cAAN,CAAD,CAAuBU,EAAvB,CAA0B5B,YAA1B,EAAwC,UAAA6B,KAAK;EAAA,aAAI,KAAI,CAACC,QAAL,CAAcD,KAAd,CAAJ;EAAA,KAA7C;EAEA,SAAKE,OAAL;;EACA,SAAKD,QAAL;EACD;;;;;EAYD;WAEAC,UAAA,mBAAU;EAAA;;EACR,QAAMC,UAAU,GAAG,KAAKd,cAAL,KAAwB,KAAKA,cAAL,CAAoBE,MAA5C,GACjBR,aADiB,GACDC,eADlB;EAGA,QAAMoB,YAAY,GAAG,KAAKZ,OAAL,CAAazB,MAAb,KAAwB,MAAxB,GACnBoC,UADmB,GACN,KAAKX,OAAL,CAAazB,MAD5B;EAGA,QAAMsC,UAAU,GAAGD,YAAY,KAAKpB,eAAjB,GACjB,KAAKsB,aAAL,EADiB,GACM,CADzB;EAGA,SAAKX,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EAEA,SAAKE,aAAL,GAAqB,KAAKS,gBAAL,EAArB;EAEA,QAAMC,OAAO,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B,KAAKlB,SAA/B,CAAd,CAAhB;EAEAc,IAAAA,OAAO,CACJK,GADH,CACO,UAAA3B,OAAO,EAAI;EACd,UAAIlB,MAAJ;EACA,UAAM8C,cAAc,GAAGC,wBAAI,CAACC,sBAAL,CAA4B9B,OAA5B,CAAvB;;EAEA,UAAI4B,cAAJ,EAAoB;EAClB9C,QAAAA,MAAM,GAAG2C,QAAQ,CAACM,aAAT,CAAuBH,cAAvB,CAAT;EACD;;EAED,UAAI9C,MAAJ,EAAY;EACV,YAAMkD,SAAS,GAAGlD,MAAM,CAACmD,qBAAP,EAAlB;;EACA,YAAID,SAAS,CAACE,KAAV,IAAmBF,SAAS,CAACG,MAAjC,EAAyC;EACvC;EACA,iBAAO,CACL1D,qBAAC,CAACK,MAAD,CAAD,CAAUoC,YAAV,IAA0BkB,GAA1B,GAAgCjB,UAD3B,EAELS,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KArBH,EAsBGS,MAtBH,CAsBU,UAAAC,IAAI;EAAA,aAAIA,IAAJ;EAAA,KAtBd,EAuBGC,IAvBH,CAuBQ,UAACC,CAAD,EAAIC,CAAJ;EAAA,aAAUD,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAC,CAAC,CAAD,CAAlB;EAAA,KAvBR,EAwBGC,OAxBH,CAwBW,UAAAJ,IAAI,EAAI;EACf,MAAA,MAAI,CAAC7B,QAAL,CAAckC,IAAd,CAAmBL,IAAI,CAAC,CAAD,CAAvB;;EACA,MAAA,MAAI,CAAC5B,QAAL,CAAciC,IAAd,CAAmBL,IAAI,CAAC,CAAD,CAAvB;EACD,KA3BH;EA4BD;;WAEDM,UAAA,mBAAU;EACRnE,IAAAA,qBAAC,CAACoE,UAAF,CAAa,KAAK3C,QAAlB,EAA4B7B,QAA5B;EACAI,IAAAA,qBAAC,CAAC,KAAK0B,cAAN,CAAD,CAAuB2C,GAAvB,CAA2BxE,SAA3B;EAEA,SAAK4B,QAAL,GAAgB,IAAhB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKG,OAAL,GAAe,IAAf;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACD;;;WAIDL,aAAA,oBAAWN,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDtB,OADC,EAEA,OAAOsB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAFhD,CAAN;;EAKA,QAAI,OAAOA,MAAM,CAACnB,MAAd,KAAyB,QAAzB,IAAqC+C,wBAAI,CAACkB,SAAL,CAAe9C,MAAM,CAACnB,MAAtB,CAAzC,EAAwE;EACtE,UAAIkE,EAAE,GAAGvE,qBAAC,CAACwB,MAAM,CAACnB,MAAR,CAAD,CAAiBmE,IAAjB,CAAsB,IAAtB,CAAT;;EACA,UAAI,CAACD,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAGnB,wBAAI,CAACqB,MAAL,CAAY/E,IAAZ,CAAL;EACAM,QAAAA,qBAAC,CAACwB,MAAM,CAACnB,MAAR,CAAD,CAAiBmE,IAAjB,CAAsB,IAAtB,EAA4BD,EAA5B;EACD;;EAED/C,MAAAA,MAAM,CAACnB,MAAP,SAAoBkE,EAApB;EACD;;EAEDnB,IAAAA,wBAAI,CAACsB,eAAL,CAAqBhF,IAArB,EAA2B8B,MAA3B,EAAmClB,WAAnC;EAEA,WAAOkB,MAAP;EACD;;WAEDmB,gBAAA,yBAAgB;EACd,WAAO,KAAKjB,cAAL,KAAwBE,MAAxB,GACL,KAAKF,cAAL,CAAoBiD,WADf,GAC6B,KAAKjD,cAAL,CAAoBkD,SADxD;EAED;;WAEDhC,mBAAA,4BAAmB;EACjB,WAAO,KAAKlB,cAAL,CAAoBmD,YAApB,IAAoCC,IAAI,CAACC,GAAL,CACzC/B,QAAQ,CAACgC,IAAT,CAAcH,YAD2B,EAEzC7B,QAAQ,CAACiC,eAAT,CAAyBJ,YAFgB,CAA3C;EAID;;WAEDK,mBAAA,4BAAmB;EACjB,WAAO,KAAKxD,cAAL,KAAwBE,MAAxB,GACLA,MAAM,CAACuD,WADF,GACgB,KAAKzD,cAAL,CAAoB8B,qBAApB,GAA4CE,MADnE;EAED;;WAEDpB,WAAA,oBAAW;EACT,QAAMsC,SAAS,GAAG,KAAKjC,aAAL,KAAuB,KAAKd,OAAL,CAAa1B,MAAtD;;EACA,QAAM0E,YAAY,GAAG,KAAKjC,gBAAL,EAArB;;EACA,QAAMwC,SAAS,GAAG,KAAKvD,OAAL,CAAa1B,MAAb,GAAsB0E,YAAtB,GAAqC,KAAKK,gBAAL,EAAvD;;EAEA,QAAI,KAAK/C,aAAL,KAAuB0C,YAA3B,EAAyC;EACvC,WAAKtC,OAAL;EACD;;EAED,QAAIqC,SAAS,IAAIQ,SAAjB,EAA4B;EAC1B,UAAM/E,MAAM,GAAG,KAAK4B,QAAL,CAAc,KAAKA,QAAL,CAAcoD,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAKnD,aAAL,KAAuB7B,MAA3B,EAAmC;EACjC,aAAKiF,SAAL,CAAejF,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAK6B,aAAL,IAAsB0C,SAAS,GAAG,KAAK5C,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKqD,MAAL;;EACA;EACD;;EAED,SAAK,IAAIC,CAAC,GAAG,KAAKxD,QAAL,CAAcqD,MAA3B,EAAmCG,CAAC,EAApC,GAAyC;EACvC,UAAMC,cAAc,GAAG,KAAKvD,aAAL,KAAuB,KAAKD,QAAL,CAAcuD,CAAd,CAAvB,IACnBZ,SAAS,IAAI,KAAK5C,QAAL,CAAcwD,CAAd,CADM,KAElB,OAAO,KAAKxD,QAAL,CAAcwD,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACGZ,SAAS,GAAG,KAAK5C,QAAL,CAAcwD,CAAC,GAAG,CAAlB,CAHG,CAAvB;;EAKA,UAAIC,cAAJ,EAAoB;EAClB,aAAKH,SAAL,CAAe,KAAKrD,QAAL,CAAcuD,CAAd,CAAf;EACD;EACF;EACF;;WAEDF,YAAA,mBAAUjF,MAAV,EAAkB;EAChB,SAAK6B,aAAL,GAAqB7B,MAArB;;EAEA,SAAKkF,MAAL;;EAEA,QAAMG,OAAO,GAAG,KAAK3D,SAAL,CACb4D,KADa,CACP,GADO,EAEbzC,GAFa,CAET,UAAA0C,QAAQ;EAAA,aAAOA,QAAP,uBAAgCvF,MAAhC,YAA4CuF,QAA5C,gBAA8DvF,MAA9D;EAAA,KAFC,CAAhB;;EAIA,QAAMwF,KAAK,GAAG7F,qBAAC,CAAC,GAAG8C,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0ByC,OAAO,CAACI,IAAR,CAAa,GAAb,CAA1B,CAAd,CAAD,CAAf;;EAEA,QAAID,KAAK,CAACE,QAAN,CAAerF,wBAAf,CAAJ,EAA8C;EAC5CmF,MAAAA,KAAK,CAACG,OAAN,CAAc/E,iBAAd,EACGgF,IADH,CACQ9E,wBADR,EAEG+E,QAFH,CAEYvF,iBAFZ;EAGAkF,MAAAA,KAAK,CAACK,QAAN,CAAevF,iBAAf;EACD,KALD,MAKO;EACL;EACAkF,MAAAA,KAAK,CAACK,QAAN,CAAevF,iBAAf,EAFK;EAIL;;EACAkF,MAAAA,KAAK,CAACM,OAAN,CAActF,uBAAd,EACGuF,IADH,CACWtF,kBADX,UACkCE,mBADlC,EAEGkF,QAFH,CAEYvF,iBAFZ,EALK;;EASLkF,MAAAA,KAAK,CAACM,OAAN,CAActF,uBAAd,EACGuF,IADH,CACQrF,kBADR,EAEGsF,QAFH,CAEYvF,kBAFZ,EAGGoF,QAHH,CAGYvF,iBAHZ;EAID;;EAEDX,IAAAA,qBAAC,CAAC,KAAK0B,cAAN,CAAD,CAAuB4E,OAAvB,CAA+B/F,cAA/B,EAA+C;EAC7CgG,MAAAA,aAAa,EAAElG;EAD8B,KAA/C;EAGD;;WAEDkF,SAAA,kBAAS;EACP,OAAGzC,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B,KAAKlB,SAA/B,CAAd,EACG6B,MADH,CACU,UAAA4C,IAAI;EAAA,aAAIA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwB/F,iBAAxB,CAAJ;EAAA,KADd,EAEGsD,OAFH,CAEW,UAAAuC,IAAI;EAAA,aAAIA,IAAI,CAACC,SAAL,CAAeE,MAAf,CAAsBhG,iBAAtB,CAAJ;EAAA,KAFf;EAGD;;;cAIMiG,mBAAP,0BAAwBpF,MAAxB,EAAgC;EAC9B,WAAO,KAAKqF,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAG9G,qBAAC,CAAC,IAAD,CAAD,CAAQ8G,IAAR,CAAalH,QAAb,CAAX;;EACA,UAAMiC,OAAO,GAAG,OAAOL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACsF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIxF,SAAJ,CAAc,IAAd,EAAoBO,OAApB,CAAP;EACA7B,QAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQ8G,IAAR,CAAalH,QAAb,EAAuBkH,IAAvB;EACD;;EAED,UAAI,OAAOtF,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsF,IAAI,CAACtF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIuF,SAAJ,wBAAkCvF,MAAlC,QAAN;EACD;;EAEDsF,QAAAA,IAAI,CAACtF,MAAD,CAAJ;EACD;EACF,KAhBM,CAAP;EAiBD;;;;0BAlNoB;EACnB,aAAO7B,OAAP;EACD;;;0BAEoB;EACnB,aAAOO,OAAP;EACD;;;;;EA+MH;;;;;;;AAMAF,uBAAC,CAAC4B,MAAD,CAAD,CAAUQ,EAAV,CAAa3B,mBAAb,EAAkC,YAAM;EACtC,MAAMuG,UAAU,GAAG,GAAGlE,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BrC,iBAA1B,CAAd,CAAnB;EACA,MAAMqG,gBAAgB,GAAGD,UAAU,CAAC3B,MAApC;;EAEA,OAAK,IAAIG,CAAC,GAAGyB,gBAAb,EAA+BzB,CAAC,EAAhC,GAAqC;EACnC,QAAM0B,IAAI,GAAGlH,qBAAC,CAACgH,UAAU,CAACxB,CAAD,CAAX,CAAd;;EACAlE,IAAAA,SAAS,CAACsF,gBAAV,CAA2B7D,IAA3B,CAAgCmE,IAAhC,EAAsCA,IAAI,CAACJ,IAAL,EAAtC;EACD;EACF,CARD;EAUA;;;;;;AAMA9G,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAa4B,SAAS,CAACsF,gBAAvB;AACA5G,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWyH,WAAX,GAAyB7F,SAAzB;;AACAtB,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAW0H,UAAX,GAAwB,YAAM;EAC5BpH,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOuB,SAAS,CAACsF,gBAAjB;EACD,CAHD;;;;;;;;"}