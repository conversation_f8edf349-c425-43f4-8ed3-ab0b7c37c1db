{"version": 3, "file": "alert.js", "sources": ["../src/alert.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    let parent = false\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    if (!parent) {\n      parent = $(element).closest(`.${CLASS_NAME_ALERT}`)[0]\n    }\n\n    return parent\n  }\n\n  _triggerCloseEvent(element) {\n    const closeEvent = $.Event(EVENT_CLOSE)\n\n    $(element).trigger(closeEvent)\n    return closeEvent\n  }\n\n  _removeElement(element) {\n    $(element).removeClass(CLASS_NAME_SHOW)\n\n    if (!$(element).hasClass(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = Util.getTransitionDurationFromElement(element)\n\n    $(element)\n      .one(Util.TRANSITION_END, event => this._destroyElement(element, event))\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  _destroyElement(element) {\n    $(element)\n      .detach()\n      .trigger(EVENT_CLOSED)\n      .remove()\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static _handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(\n  EVENT_CLICK_DATA_API,\n  SELECTOR_DISMISS,\n  Alert._handleDismiss(new Alert())\n)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Alert._jQueryInterface\n$.fn[NAME].Constructor = Alert\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Alert._jQueryInterface\n}\n\nexport default Alert\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "element", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "parent", "document", "querySelector", "closest", "closeEvent", "Event", "trigger", "removeClass", "hasClass", "_destroyElement", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "event", "emulateTransitionEnd", "detach", "remove", "_jQueryInterface", "config", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAG,OAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,UAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,gBAAgB,GAAG,wBAAzB;EAEA,IAAMC,WAAW,aAAWN,SAA5B;EACA,IAAMO,YAAY,cAAYP,SAA9B;EACA,IAAMQ,oBAAoB,aAAWR,SAAX,GAAuBC,YAAjD;EAEA,IAAMQ,gBAAgB,GAAG,OAAzB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EAEA;;;;;;MAMMC;EACJ,iBAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACD;;;;;EAQD;WAEAE,QAAA,eAAMF,OAAN,EAAe;EACb,QAAIG,WAAW,GAAG,KAAKF,QAAvB;;EACA,QAAID,OAAJ,EAAa;EACXG,MAAAA,WAAW,GAAG,KAAKC,eAAL,CAAqBJ,OAArB,CAAd;EACD;;EAED,QAAMK,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,CAACE,kBAAZ,EAAJ,EAAsC;EACpC;EACD;;EAED,SAAKC,cAAL,CAAoBL,WAApB;EACD;;WAEDM,UAAA,mBAAU;EACRnB,IAAAA,qBAAC,CAACoB,UAAF,CAAa,KAAKT,QAAlB,EAA4Bf,QAA5B;EACA,SAAKe,QAAL,GAAgB,IAAhB;EACD;;;WAIDG,kBAAA,yBAAgBJ,OAAhB,EAAyB;EACvB,QAAMW,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4Bb,OAA5B,CAAjB;EACA,QAAIc,MAAM,GAAG,KAAb;;EAEA,QAAIH,QAAJ,EAAc;EACZG,MAAAA,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuBL,QAAvB,CAAT;EACD;;EAED,QAAI,CAACG,MAAL,EAAa;EACXA,MAAAA,MAAM,GAAGxB,qBAAC,CAACU,OAAD,CAAD,CAAWiB,OAAX,OAAuBrB,gBAAvB,EAA2C,CAA3C,CAAT;EACD;;EAED,WAAOkB,MAAP;EACD;;WAEDR,qBAAA,4BAAmBN,OAAnB,EAA4B;EAC1B,QAAMkB,UAAU,GAAG5B,qBAAC,CAAC6B,KAAF,CAAQ1B,WAAR,CAAnB;EAEAH,IAAAA,qBAAC,CAACU,OAAD,CAAD,CAAWoB,OAAX,CAAmBF,UAAnB;EACA,WAAOA,UAAP;EACD;;WAEDV,iBAAA,wBAAeR,OAAf,EAAwB;EAAA;;EACtBV,IAAAA,qBAAC,CAACU,OAAD,CAAD,CAAWqB,WAAX,CAAuBvB,eAAvB;;EAEA,QAAI,CAACR,qBAAC,CAACU,OAAD,CAAD,CAAWsB,QAAX,CAAoBzB,eAApB,CAAL,EAA2C;EACzC,WAAK0B,eAAL,CAAqBvB,OAArB;;EACA;EACD;;EAED,QAAMwB,kBAAkB,GAAGZ,wBAAI,CAACa,gCAAL,CAAsCzB,OAAtC,CAA3B;EAEAV,IAAAA,qBAAC,CAACU,OAAD,CAAD,CACG0B,GADH,CACOd,wBAAI,CAACe,cADZ,EAC4B,UAAAC,KAAK;EAAA,aAAI,KAAI,CAACL,eAAL,CAAqBvB,OAArB,EAA8B4B,KAA9B,CAAJ;EAAA,KADjC,EAEGC,oBAFH,CAEwBL,kBAFxB;EAGD;;WAEDD,kBAAA,yBAAgBvB,OAAhB,EAAyB;EACvBV,IAAAA,qBAAC,CAACU,OAAD,CAAD,CACG8B,MADH,GAEGV,OAFH,CAEW1B,YAFX,EAGGqC,MAHH;EAID;;;UAIMC,mBAAP,0BAAwBC,MAAxB,EAAgC;EAC9B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAMC,QAAQ,GAAG7C,qBAAC,CAAC,IAAD,CAAlB;EACA,UAAI8C,IAAI,GAAGD,QAAQ,CAACC,IAAT,CAAclD,QAAd,CAAX;;EAEA,UAAI,CAACkD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIrC,KAAJ,CAAU,IAAV,CAAP;EACAoC,QAAAA,QAAQ,CAACC,IAAT,CAAclD,QAAd,EAAwBkD,IAAxB;EACD;;EAED,UAAIH,MAAM,KAAK,OAAf,EAAwB;EACtBG,QAAAA,IAAI,CAACH,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAZM,CAAP;EAaD;;UAEMI,iBAAP,wBAAsBC,aAAtB,EAAqC;EACnC,WAAO,UAAUV,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAACW,cAAN;EACD;;EAEDD,MAAAA,aAAa,CAACpC,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;;;0BAlGoB;EACnB,aAAOjB,OAAP;EACD;;;;;EAmGH;;;;;;;AAMAK,uBAAC,CAACyB,QAAD,CAAD,CAAYyB,EAAZ,CACE7C,oBADF,EAEEH,gBAFF,EAGEO,KAAK,CAACsC,cAAN,CAAqB,IAAItC,KAAJ,EAArB,CAHF;EAMA;;;;;;AAMAT,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAae,KAAK,CAACiC,gBAAnB;AACA1C,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWyD,WAAX,GAAyB1C,KAAzB;;AACAT,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAW0D,UAAX,GAAwB,YAAM;EAC5BpD,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOU,KAAK,CAACiC,gBAAb;EACD,CAHD;;;;;;;;"}