// main: ../main.scss
/*--------------------------------------------------------------
# Blog Pagination Section
--------------------------------------------------------------*/
.blog-pagination {
  padding-top: 0;
  color: color-mix(in srgb, var(--default-color), transparent 40%);

  ul {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
  }

  li {
    margin: 0 5px;
    transition: 0.3s;

    a {
      color: color-mix(in srgb, var(--default-color), transparent 40%);
      padding: 7px 16px;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active,
      &:hover {
        background: var(--accent-color);
        color: var(--contrast-color);

        a {
          color: var(--contrast-color);
        }
      }
    }
  }
}