<div style='padding:20px; border-top:1px dotted lightgray;'>
 <p><b>Information that visitors post on your website. to Approve or Delete (Click the x or the tick).</b></p>
</div>
<div style='padding:20px; border-top:1px dotted lightgray;'>
<?

// database connection info
include'function.php';

// find out how many rows are in the table
$sql = "SELECT COUNT(*) FROM news";
$result = mysql_query($sql) or trigger_error("SQL", E_USER_ERROR);
$r = mysql_fetch_row($result);
$numrows = $r[0];

// number of rows to show per page
$rowsperpage = 4;
// find out total pages
$totalpages = ceil($numrows / $rowsperpage);

// get the current page or set a default
if (isset($_GET['currentpage']) && is_numeric($_GET['currentpage'])) {
   // cast var as int
   $currentpage = (int) $_GET['currentpage'];
} else {
   // default page num
   $currentpage = 1;
} // end if

// if current page is greater than total pages...
if ($currentpage > $totalpages) {
   // set current page to last page
   $currentpage = $totalpages;
} // end if
// if current page is less than first page...
if ($currentpage < 1) {
   // set current page to first page
   $currentpage = 1;
} // end if

// the offset of the list, based on current page
$offset = ($currentpage - 1) * $rowsperpage;

//count the amount of clients

// get the info from the db
$sql = "SELECT * FROM news ORDER BY date DESC LIMIT $offset, $rowsperpage ";
$result = mysql_query($sql) or trigger_error("SQL", E_USER_ERROR);

    while ($rows = mysql_fetch_assoc($result))
    {
      $id = $rows['id'];
     $title = $rows['title'];
     $body = $rows['body'];
     $images = $rows['image'];
     $name = $rows['firstname'];
     $lastn = $rows['lastname'];
     $status = $rows['status'];

        $lowercase = strtolower($email);
        $imagecode = md5( $lowercase );


     echo"
    <div class='message-image' >
";
if(($images=="")||($images=="images/")||($images=="../images/"))

{echo"<img border='0' src='../images/02020.jpg' width='57'>";}
else
{echo"<img border='0' src='$images' width='57'>";}echo"
              
</div>

    <div class='message'>
    <font  color='#000'><b> $name $lastn </b></font>
    <br/>";
    echo "<div class='message-row'><font>"; echo nl2br($body);

    echo"</font></div>";

     $time = time();
     //data
   $get_time = $rows['date'];

   $diff = $time - $get_time;

      switch(1)
      {
        case($diff < 60):
        $count = $diff;
        if($count==0)
        $count = "a moment";
        else if ($count==1)
        $suffix = "second";
        else
        $suffix = "seconds";
        break;

        case($diff > 60 && $diff < 3600):
        $count = floor($diff/60);
        if($count==1)
        $suffix = "minute";
        else
        $suffix = "minutes";
        break;

        case($diff > 3600 && $diff < 86400):
        $count = floor($diff/3600);
        if($count==1)
        $suffix = "hour";
        else
        $suffix = "hours";
        break;

        case($diff > 86400 && $diff < 2629743):
        $count = floor($diff/86400);
        if($count==1)
        $suffix = "day";
        else
        $suffix = "days";
        break;

        case($diff > 2629743 && $diff < 31556926):
        $count = floor($diff/2629743);
        if($count==1)
        $suffix = "month";
        else
        $suffix = "months";
        break;

         case($diff > 31556926):
        $count = floor($diff/31556926);
        if($count==1)
        $suffix = "year";
        else
        $suffix = "years";
        break;
      }
          echo"<font color='#969696'><br/>&ensp; ".$count." ".$suffix."</font>";
          echo"&nbsp";

          echo"<a href=\"cpanel.php?p=wall_delete&id=".$id."\"><img src='../images/x.png' border='0px'></a>
               <a href=\"cpanel.php?p=wall_update&id=".$id."\"><img src='../images/tick.png' border='0px'></a>
                ";if($status==0){echo'Not-Active';}else{echo'Active';}echo"
          <br/>";
        echo"</div><br /><br/>";

    }
// end while

/******  build the pagination links ******/
// range of num links to show
$range = 3;

// if not on page 1, don't show back links
if ($currentpage > 1) {
   // show << link to go back to page 1
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=wall&currentpage=1'>First</a> ";
   // get previous page num
   $prevpage = $currentpage - 1;
   // show < link to go back to 1 page
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=wall&currentpage=$prevpage'>Prev</a> ";
} // end if

// loop to show links to range of pages around current page
for ($x = ($currentpage - $range); $x < (($currentpage + $range) + 1); $x++) {
   // if it's a valid page number...
   if (($x > 0) && ($x <= $totalpages)) {
      // if we're on current page...
      if ($x == $currentpage) {
         // 'highlight' it but don't make a link
         echo " [<b>$x</b>] ";
      // if not current page...
      } else {
         // make it a link
         echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=wall&currentpage=$x'>$x</a> ";
      } // end else
   } // end if
} // end for

// if not on last page, show forward and last page links
if ($currentpage != $totalpages) {
   // get next page
   $nextpage = $currentpage + 1;
    // echo forward link for next page
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=wall&currentpage=$nextpage'>Next</a> ";
   // echo forward link for lastpage
   echo " <a class='button' href='{$_SERVER['PHP_SELF']}?p=wall&currentpage=$totalpages'>Last</a> ";
} // end if
/****** end build pagination links ******/
 ?>
</div>