// main: ../main.scss
/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;

    ul {
      margin: 0;
      padding: 0;
      display: flex;
      list-style: none;
      align-items: center;
    }

    li {
      position: relative;
    }

    a,
    a:focus {
      color: var(--nav-color);
      padding: 18px 15px;
      font-size: 16px;
      font-family: var(--nav-font);
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: space-between;
      white-space: nowrap;
      transition: 0.3s;

      i {
        font-size: 12px;
        line-height: 0;
        margin-left: 5px;
        transition: 0.3s;
      }
    }

    li:last-child a {
      padding-right: 0;
    }

    li:hover>a,
    .active,
    .active:focus {
      color: var(--nav-hover-color);
    }

    .dropdown {
      ul {
        margin: 0;
        padding: 10px 0;
        background: var(--nav-dropdown-background-color);
        display: block;
        position: absolute;
        visibility: hidden;
        left: 14px;
        top: 130%;
        opacity: 0;
        transition: 0.3s;
        border-radius: 4px;
        z-index: 99;
        box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);

        li {
          min-width: 200px;
        }

        a {
          padding: 10px 20px;
          font-size: 15px;
          text-transform: none;
          color: var(--nav-dropdown-color);

          i {
            font-size: 12px;
          }
        }

        a:hover,
        .active:hover,
        li:hover>a {
          color: var(--nav-dropdown-hover-color);
        }
      }

      &:hover>ul {
        opacity: 1;
        top: 100%;
        visibility: visible;
      }

      .dropdown {
        ul {
          top: 0;
          left: -90%;
          visibility: hidden;
        }

        &:hover>ul {
          opacity: 1;
          top: 0;
          left: -100%;
          visibility: visible;
        }
      }
    }
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;

    ul {
      display: none;
      list-style: none;
      position: absolute;
      inset: 60px 20px 20px 20px;
      padding: 10px 0;
      margin: 0;
      border-radius: 6px;
      background-color: var(--nav-mobile-background-color);
      overflow-y: auto;
      transition: 0.3s;
      z-index: 9998;
      box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
    }

    a,
    a:focus {
      color: var(--nav-color);
      padding: 10px 20px;
      font-family: var(--nav-font);
      font-size: 17px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: space-between;
      white-space: nowrap;
      transition: 0.3s;

      i {
        font-size: 12px;
        line-height: 0;
        margin-left: 5px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: 0.3s;
        background-color: color-mix(in srgb, var(--accent-color), transparent 90%);

        &:hover {
          background-color: var(--accent-color);
          color: var(--contrast-color);
        }
      }
    }

    a:hover,
    .active,
    .active:focus {
      color: var(--nav-hover-color);
    }

    .active i,
    .active:focus i {
      background-color: var(--accent-color);
      color: var(--contrast-color);
      transform: rotate(180deg);
    }

    .dropdown {
      ul {
        position: static;
        display: none;
        z-index: 99;
        padding: 10px 0;
        margin: 10px 20px;
        background-color: var(--nav-dropdown-background-color);
        border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
        box-shadow: none;
        transition: all .5s ease-in-out;

        ul {
          background-color: rgba(33, 37, 41, 0.1);
        }

        a {
          color: var(--nav-dropdown-color);
        }

        a:hover,
        .active:hover,
        li:hover>a {
          color: var(--nav-dropdown-hover-color);
        }
      }

      >.dropdown-active {
        display: block;
        background-color: rgba(33, 37, 41, 0.03);
      }
    }
  }

  .mobile-nav-active {
    overflow: hidden;

    .mobile-nav-toggle {
      color: #fff;
      position: absolute;
      font-size: 32px;
      top: 15px;
      right: 15px;
      margin-right: 0;
      z-index: 9999;
    }

    .navmenu {
      position: fixed;
      overflow: hidden;
      inset: 0;
      background: rgba(33, 37, 41, 0.8);
      transition: 0.3s;

      >ul {
        display: block;
      }
    }
  }
}