<?php
session_start();

$session_id    = $_SESSION['username'];
$session_login = $_SESSION['email'];
$session_admin = $_SESSION['admin_id'];

require'./function.php';

function loggedIn($id)
{
  $premium = mysql_query("SELECT id, username FROM clients WHERE id='$id' AND username='$session_id'");
  if (mysql_num_rows($premium)==1)
     return true;
   else
     return false;
}
   function isPremium($id)
{
  $premium = mysql_query("SELECT id, email FROM employers WHERE id='$id' AND email='$session_login' ");
  if (mysql_num_rows($premium)==1)
     return true;
   else
     return false;
}

    function isAdmin($id)
{
  $premium = mysql_query("SELECT id, admin_id, password FROM admin WHERE id='$id' AND admin_id='$session_admin'");
  if (mysql_num_rows($premium)==1)
     return true;
   else
     return false;
}

?>
