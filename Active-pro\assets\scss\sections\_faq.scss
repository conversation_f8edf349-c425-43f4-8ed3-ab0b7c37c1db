// main: ../main.scss
/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/
.faq {
  .content-subtitle {
    font-size: 15px;
    margin-bottom: 10px;
    display: block;
    color: var(--default-color);
  }

  .content-title {
    color: var(--heading-color);
    font-size: 22px;
    margin-bottom: 30px;
  }

  p {
    line-height: 1.7;
    color: var(--default-color);
  }

  .custom-accordion {
    .accordion-item {
      background-color: var(--surface-color);
      margin-bottom: 0px;
      position: relative;
      border-radius: 0px;
      overflow: hidden;

      .btn-link {
        display: block;
        width: 100%;
        padding: 15px 0;
        text-decoration: none;
        text-align: left;
        color: var(--default-color);
        border: none;
        padding-left: 40px;
        border-radius: 0;
        position: relative;
        background-color: color-mix(in srgb, var(--default-color), transparent 94%);

        &:before {
          content: "\f282";
          display: inline-block;
          font-family: "bootstrap-icons" !important;
          font-style: normal;
          font-weight: normal !important;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          vertical-align: -0.125em;
          -webkit-font-smoothing: antialiased;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 15px;
        }

        &[aria-expanded="true"] {
          color: var(--accent-color);

          &:before {
            font-family: "bootstrap-icons" !important;
            content: "\f286";
            position: absolute;
            color: var(--accent-color);
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }

      .accordion-body {
        padding: 20px 20px 20px 20px;
        color: var(--default-color);
      }
    }
  }
}