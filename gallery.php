

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> Image Gallery</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            display: flex;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .upload-form-section {
            flex: 1;
            padding: 20px;
            border-right: 1px solid #eee;
        }
        .gallery-section {
            flex: 2;
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
        }
        .gallery-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            background-color: #f9f9f9;
        }
        .gallery-item img {
            max-width: 100%;
            height: 150px; /* Fixed height for consistent display */
            object-fit: cover; /* Crop images to fit */
            border-radius: 4px;
            margin-bottom: 8px;
        }
        .gallery-item p {
            font-size: 0.9em;
            color: #555;
            margin: 0;
            word-wrap: break-word; /* Prevent long words from overflowing */
        }
        h2 {
            color: #0056b3;
        }
        form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        form input[type="file"],
        form input[type="text"],
        form textarea {
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box; /* Include padding and border in element's total width and height */
        }
        form textarea {
            resize: vertical;
            min-height: 80px;
        }
        form input[type="submit"] {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
        }
        form input[type="submit"]:hover {
            background-color: #0056b3;
        }
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>

    <div class="container">
        <div class="upload-form-section">
            <h2>Upload New Image</h2>
            <?php if (!empty($upload_message)): ?>
                <div class="message <?php echo strpos($upload_message, 'Error') !== false ? 'error' : 'success'; ?>">
                    <?php echo $upload_message; ?>
                </div>
            <?php endif; ?>
            <form action="index.php" method="post" enctype="multipart/form-data">
                <label for="image_file">Select Image:</label>
                <input type="file" name="image_file" id="image_file" accept="image/*" required>

                <label for="caption">Caption (optional):</label>
                <input type="text" name="caption" id="caption" maxlength="255">

                <label for="description">Description (optional):</label>
                <textarea name="description" id="description"></textarea>

                <input type="submit" value="Upload Image">
            </form>
        </div>

        <div class="gallery-section">
            <h2>Your Gallery</h2>
            <?php if (isset($gallery_error)): ?>
                <div class="message error"><?php echo $gallery_error; ?></div>
            <?php elseif (empty($images)): ?>
                <p>No images in the gallery yet. Upload one!</p>
            <?php else: ?>
                <?php foreach ($images as $image): ?>
                    <div class="gallery-item">
                        <img src="<?php echo htmlspecialchars($target_dir . $image['filename']); ?>"
                             alt="<?php echo htmlspecialchars($image['caption'] ?: 'Gallery Image'); ?>"
                             title="<?php echo htmlspecialchars($image['description'] ?: $image['caption']); ?>">
                        <p><?php echo htmlspecialchars($image['caption'] ?: 'No Caption'); ?></p>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

</body>
</html>