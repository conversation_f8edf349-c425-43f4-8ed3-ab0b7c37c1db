<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="../../../dist/css/bootstrap.min.css">
    <title>Carousel</title>
    <style>
      .carousel-item {
        transition: transform 2s ease, opacity .5s ease;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Carousel <small>Bootstrap Visual Test</small></h1>

      <p>The transition duration should be around 2s. Also, the carousel shouldn't slide when its window/tab is hidden. Check the console log.</p>

      <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
        <ol class="carousel-indicators">
          <li data-target="#carousel-example-generic" data-slide-to="0" class="active"></li>
          <li data-target="#carousel-example-generic" data-slide-to="1"></li>
          <li data-target="#carousel-example-generic" data-slide-to="2"></li>
        </ol>
        <div class="carousel-inner">
          <div class="carousel-item active">
            <img src="https://i.imgur.com/iEZgY7Y.jpg" alt="First slide">
          </div>
          <div class="carousel-item">
            <img src="https://i.imgur.com/eNWn1Xs.jpg" alt="Second slide">
          </div>
          <div class="carousel-item">
            <img src="https://i.imgur.com/Nm7xoti.jpg" alt="Third slide">
          </div>
        </div>
        <a class="carousel-control-prev" href="#carousel-example-generic" role="button" data-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="sr-only">Previous</span>
        </a>
        <a class="carousel-control-next" href="#carousel-example-generic" role="button" data-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="sr-only">Next</span>
        </a>
      </div>
    </div>

    <script src="../../../node_modules/jquery/dist/jquery.slim.min.js"></script>
    <script src="../../dist/util.js"></script>
    <script src="../../dist/carousel.js"></script>
    <script>
      $(function() {
        var t0, t1;

        // Test to show that the carousel doesn't slide when the current tab isn't visible
        // Test to show that transition-duration can be changed with css
        $('#carousel-example-generic').on('slid.bs.carousel', function(event) {
          t1 = performance.now()
          console.log('transition-duration took' + (t1 - t0) + 'ms, slid at ', event.timeStamp)
        }).on('slide.bs.carousel', function() {
          t0 = performance.now()
        })
      })
    </script>
  </body>
</html>
