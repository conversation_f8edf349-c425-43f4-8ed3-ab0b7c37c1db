<?php

include './function2.php';

 if(isAdmin($session_admin))
     {
     header("location: cpanel.php");
     exit();
     }

if($_POST['login'])
{
   $email = $_POST['admin_id'];
   $password = $_POST['password'];
   $rememberme = $_POST['rememberme'];


   if ($email&&$password)
       {
       $login = mysql_query("SELECT * FROM admin WHERE admin_id='$email'");

       while($row = mysql_fetch_assoc($login))


       {
            $db_password = $row['password'];
             $activated = $row['activated'];
             if ($activated=='0')
             {

             echo"your account is not activated, please check your email";
             exit();
             }

         if (md5($password)==$db_password)

             $loginok = True;
         else
              $loginok = FALSE;

          if  ($loginok==True)
             {
          if ($rememberme=="on")
          setcookie("admin_id", $email, time()+7200);
          else if ($rememberme=="")
          $_SESSION['admin_id']=$email;

          $date = date("Y-m-d, h:i:s");
          $ip   = $_SERVER['REMOTE_ADDR'];

$update = mysql_query("UPDATE admin SET date_last_access='$date', ipaddress='$ip' WHERE admin_id='$email'");


          header("location: cpanel.php");
          exit();
              }
       }
     echo"<div class='warning' align='center'>
               Incorrect email address or an password.<a href='#' onclick='history.go(-1);return false;'>Back</a>
               </div>";
      }

   else{

     echo "<div class='warning' align='center'>Please enter a email address and password.</div>";
      }
}
?>
