<div class='main-content'>
<h3> Add Teacher Information </h3>
<p> Once your have submitted the info click on view to see all teachers</p>
<?php
include'./function.php';
  

 if($_POST['submit'])
 {
    $name     = $_POST['name'];
    $class    = $_POST['class'];
    $content  = $_POST['content'];
    
    $names        = $_FILES['myfile']['name'];
    $tmp_names    = $_FILES['myfile']['tmp_name'];
    
// Start Upload process
         $img = "./../images/teacher/$names";   
          
          move_uploaded_file($tmp_names, $img);

   $update = mysql_query("INSERT into team VALUE('','$content','$img','$name','$class')");
  
   echo'<div class="warning">Your Information has been Publish.</div>';

 }


   echo"
<form id='' action='' method='POST' enctype='multipart/form-data'>

<br/><br/>
<b>Name</b>: <input name='name' type='text' placeholder='Teacher name' required>

<br/><br/>
<b>Class</b>: <input name='class' type='text' placeholder='Class room ' required>

<br/><br/>
<b>Description</b>: <textarea id='editor2'  name='content' cols='80' rows='15' value='$content' required>$content</textarea><br/>

<br/>
<b>Images</b>:<input type='file'  name='myfile'>
<br/><br/>
<input type='submit' name='submit'  class='button' value='Publish'>

 </form>";

?>
</div>
<div class="right-menu">
      <div class="container-fluid">

<div id="main-navigation">

    <h4>Edit Section</h4>

<ul class="nav nav-stacked">
    <ul class="nav nav-stacked">
   
    <li class="active">
      <a href="?p=teachers_profile"><img src='../images/cpanel/list.png' width='16'> View </a>
    </li>
    <li>
      <a href="?p=teachers"><img src='../images/cpanel/add.png' width='16'> Add </a>
    </li>
    <li>
      <a href="?p=teachers_edit"> <img src='../images/cpanel/edit.png' width='16'> Edit </a>
    </li>

</ul>
</ul>

</div>
</div>
</div>