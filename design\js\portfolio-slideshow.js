/**
 * Portfolio Slideshow Enhancement Script
 * Adds smooth interactions and performance optimizations
 */

document.addEventListener('DOMContentLoaded', function() {
    const slideshowContainer = document.querySelector('.slideshow-container');
    const slideshowTrack = document.querySelector('.slideshow-track');
    
    if (!slideshowContainer || !slideshowTrack) {
        return; // Exit if elements don't exist
    }

    // Optimize performance by reducing animation complexity on slower devices
    function optimizeForDevice() {
        const isSlowDevice = navigator.hardwareConcurrency <= 2 || 
                           /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        if (isSlowDevice) {
            slideshowTrack.style.animationDuration = '40s'; // Slower animation for mobile
        }
    }

    // Pause animation when user is interacting with items
    let interactionTimeout;
    
    function pauseAnimation() {
        slideshowTrack.style.animationPlayState = 'paused';
        clearTimeout(interactionTimeout);
        
        // Resume after 3 seconds of no interaction
        interactionTimeout = setTimeout(() => {
            slideshowTrack.style.animationPlayState = 'running';
        }, 3000);
    }

    function resumeAnimation() {
        slideshowTrack.style.animationPlayState = 'running';
        clearTimeout(interactionTimeout);
    }

    // Add event listeners for better user experience
    slideshowContainer.addEventListener('mouseenter', pauseAnimation);
    slideshowContainer.addEventListener('mouseleave', resumeAnimation);
    
    // Touch events for mobile
    slideshowContainer.addEventListener('touchstart', pauseAnimation);
    slideshowContainer.addEventListener('touchend', () => {
        interactionTimeout = setTimeout(resumeAnimation, 2000);
    });

    // Intersection Observer for performance - pause when not visible
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    slideshowTrack.style.animationPlayState = 'running';
                } else {
                    slideshowTrack.style.animationPlayState = 'paused';
                }
            });
        }, {
            threshold: 0.1
        });

        observer.observe(slideshowContainer);
    }

    // Initialize optimizations
    optimizeForDevice();

    // Preload images for smoother experience
    function preloadImages() {
        const images = slideshowContainer.querySelectorAll('img');
        images.forEach(img => {
            if (!img.complete) {
                const imageLoader = new Image();
                imageLoader.src = img.src;
            }
        });
    }

    // Add loading state management
    function handleLoadingState() {
        const images = slideshowContainer.querySelectorAll('img');
        let loadedCount = 0;
        const totalImages = images.length;

        if (totalImages === 0) return;

        // Add loading class
        slideshowContainer.classList.add('loading');

        images.forEach(img => {
            if (img.complete) {
                loadedCount++;
            } else {
                img.addEventListener('load', () => {
                    loadedCount++;
                    if (loadedCount === totalImages) {
                        slideshowContainer.classList.remove('loading');
                        slideshowContainer.classList.add('loaded');
                    }
                });
                
                img.addEventListener('error', () => {
                    loadedCount++;
                    if (loadedCount === totalImages) {
                        slideshowContainer.classList.remove('loading');
                        slideshowContainer.classList.add('loaded');
                    }
                });
            }
        });

        // Remove loading state if all images are already loaded
        if (loadedCount === totalImages) {
            slideshowContainer.classList.remove('loading');
            slideshowContainer.classList.add('loaded');
        }
    }

    // Initialize loading state and preload
    handleLoadingState();
    preloadImages();

    // Add smooth scroll behavior for portfolio links
    const portfolioLinks = slideshowContainer.querySelectorAll('.portfolio-links a');
    portfolioLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add a subtle click effect
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Add keyboard navigation support
    slideshowContainer.addEventListener('keydown', function(e) {
        if (e.key === ' ' || e.key === 'Spacebar') {
            e.preventDefault();
            if (slideshowTrack.style.animationPlayState === 'paused') {
                resumeAnimation();
            } else {
                pauseAnimation();
            }
        }
    });

    // Make container focusable for keyboard navigation
    slideshowContainer.setAttribute('tabindex', '0');
    slideshowContainer.setAttribute('aria-label', 'Portfolio slideshow - Press space to pause/resume');

    console.log('Portfolio slideshow initialized successfully');
});
