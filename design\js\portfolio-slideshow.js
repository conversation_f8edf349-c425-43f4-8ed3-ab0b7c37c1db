/**
 * Portfolio Slideshow Enhancement Script
 * Adds smooth interactions and performance optimizations
 */

document.addEventListener("DOMContentLoaded", function () {
  const slideshowContainer = document.querySelector(".slideshow-container");
  const slideshowTrack = document.querySelector(".slideshow-track");

  if (!slideshowContainer || !slideshowTrack) {
    return; // Exit if elements don't exist
  }

  // Optimize performance by reducing animation complexity on slower devices
  function optimizeForDevice() {
    const isSlowDevice =
      navigator.hardwareConcurrency <= 2 ||
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );

    if (isSlowDevice) {
      slideshowTrack.style.animationDuration = "40s"; // Slower animation for mobile
    }
  }

  // Keep animation running continuously
  function ensureAnimationRunning() {
    slideshowTrack.style.animationPlayState = "running";
  }

  // Ensure animation stays running on any interaction
  slideshowContainer.addEventListener("mouseenter", ensureAnimationRunning);
  slideshowContainer.addEventListener("mouseleave", ensureAnimationRunning);

  // Touch events for mobile - keep animation running
  slideshowContainer.addEventListener("touchstart", ensureAnimationRunning);
  slideshowContainer.addEventListener("touchend", ensureAnimationRunning);

  // Intersection Observer for performance - pause when not visible
  if ("IntersectionObserver" in window) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            slideshowTrack.style.animationPlayState = "running";
          } else {
            slideshowTrack.style.animationPlayState = "paused";
          }
        });
      },
      {
        threshold: 0.1,
      }
    );

    observer.observe(slideshowContainer);
  }

  // Initialize optimizations
  optimizeForDevice();

  // Ensure perfect loop timing by monitoring animation
  function ensureSmoothLoop() {
    // Force hardware acceleration and prevent visual glitches
    slideshowTrack.style.transform = slideshowTrack.style.transform;

    // Ensure animation stays perfectly timed
    slideshowTrack.addEventListener("animationiteration", () => {
      slideshowTrack.style.animationPlayState = "running";
    });
  }

  // Call smooth loop optimization
  ensureSmoothLoop();

  // Portfolio Modal Functionality
  function initPortfolioModal() {
    const modal = document.getElementById("portfolioModal");
    const modalImage = document.getElementById("modalImage");
    const modalTitle = document.getElementById("modalTitle");
    const modalDescription = document.getElementById("modalDescription");
    const modalClose = document.getElementById("modalClose");
    const portfolioItems = document.querySelectorAll("[data-portfolio-item]");

    if (
      !modal ||
      !modalImage ||
      !modalTitle ||
      !modalDescription ||
      !modalClose
    ) {
      return; // Exit if modal elements don't exist
    }

    // Add click event to all portfolio items
    portfolioItems.forEach((item) => {
      item.addEventListener("click", function (e) {
        e.preventDefault();

        const img = this.querySelector("img");
        if (!img) return;

        const title = img.getAttribute("data-title") || "";
        const description = img.getAttribute("data-description") || "";
        const fullImage = img.getAttribute("data-full-image") || img.src;

        // Populate modal content
        modalImage.src = fullImage;
        modalImage.alt = title;
        modalTitle.textContent = title;
        modalDescription.textContent = description;

        // Show modal
        modal.classList.add("active");
        document.body.style.overflow = "hidden"; // Prevent background scrolling
      });
    });

    // Close modal functionality
    function closeModal() {
      modal.classList.remove("active");
      document.body.style.overflow = ""; // Restore scrolling
    }

    // Close button click
    modalClose.addEventListener("click", closeModal);

    // Click outside modal to close
    modal.addEventListener("click", function (e) {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Escape key to close
    document.addEventListener("keydown", function (e) {
      if (e.key === "Escape" && modal.classList.contains("active")) {
        closeModal();
      }
    });
  }

  // Initialize modal functionality
  initPortfolioModal();

  // Preload images for smoother experience
  function preloadImages() {
    const images = slideshowContainer.querySelectorAll("img");
    images.forEach((img) => {
      if (!img.complete) {
        const imageLoader = new Image();
        imageLoader.src = img.src;
      }
    });
  }

  // Add loading state management
  function handleLoadingState() {
    const images = slideshowContainer.querySelectorAll("img");
    let loadedCount = 0;
    const totalImages = images.length;

    if (totalImages === 0) return;

    // Add loading class
    slideshowContainer.classList.add("loading");

    images.forEach((img) => {
      if (img.complete) {
        loadedCount++;
      } else {
        img.addEventListener("load", () => {
          loadedCount++;
          if (loadedCount === totalImages) {
            slideshowContainer.classList.remove("loading");
            slideshowContainer.classList.add("loaded");
          }
        });

        img.addEventListener("error", () => {
          loadedCount++;
          if (loadedCount === totalImages) {
            slideshowContainer.classList.remove("loading");
            slideshowContainer.classList.add("loaded");
          }
        });
      }
    });

    // Remove loading state if all images are already loaded
    if (loadedCount === totalImages) {
      slideshowContainer.classList.remove("loading");
      slideshowContainer.classList.add("loaded");
    }
  }

  // Initialize loading state and preload
  handleLoadingState();
  preloadImages();

  // Add smooth scroll behavior for portfolio links
  const portfolioLinks =
    slideshowContainer.querySelectorAll(".portfolio-links a");
  portfolioLinks.forEach((link) => {
    link.addEventListener("click", function () {
      // Add a subtle click effect
      this.style.transform = "scale(0.95)";
      setTimeout(() => {
        this.style.transform = "";
      }, 150);
    });
  });

  // Make container focusable for accessibility
  slideshowContainer.setAttribute("tabindex", "0");
  slideshowContainer.setAttribute(
    "aria-label",
    "Portfolio slideshow - continuously scrolling showcase"
  );

  console.log("Portfolio slideshow initialized successfully");
});
