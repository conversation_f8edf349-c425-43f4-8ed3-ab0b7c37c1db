// main: ../main.scss
/*--------------------------------------------------------------
# Portfolio Details Section
--------------------------------------------------------------*/
.portfolio-details {
  .portfolio-details-slider {
    img {
      width: 100%;
    }
  }

  .swiper-wrapper {
    height: auto;
  }

  .swiper-button-prev,
  .swiper-button-next {
    width: 48px;
    height: 48px;
  }

  .swiper-button-prev:after,
  .swiper-button-next:after {
    color: rgba(255, 255, 255, .8);
    background-color: rgba(0, 0, 0, .15);
    font-size: 24px;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
  }

  .swiper-button-prev:hover,
  .swiper-button-next:hover {
    &:after {
      background-color: rgba(0, 0, 0, .3);
    }
  }

  @media (max-width: 575px) {

    .swiper-button-prev,
    .swiper-button-next {
      display: none;
    }
  }

  .swiper-pagination {
    margin-top: 20px;
    position: relative;

    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      background-color: color-mix(in srgb, var(--default-color), transparent 85%);
      opacity: 1;
    }

    .swiper-pagination-bullet-active {
      background-color: var(--accent-color);
    }
  }

  .portfolio-info {
    h3 {
      font-size: 22px;
      font-weight: 700;
      margin-bottom: 20px;
      padding-bottom: 20px;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        display: block;
        width: 50px;
        height: 3px;
        background: var(--accent-color);
        left: 0;
        bottom: 0;
      }
    }

    ul {
      list-style: none;
      padding: 0;
      font-size: 15px;

      li {
        display: flex;
        flex-direction: column;
        padding-bottom: 15px;
      }

      strong {
        text-transform: uppercase;
        font-weight: 400;
        color: color-mix(in srgb, var(--default-color), transparent 50%);
        font-size: 14px;
      }
    }

    .btn-visit {
      padding: 8px 40px;
      background: var(--accent-color);
      color: var(--contrast-color);
      border-radius: 50px;
      transition: 0.3s;

      &:hover {
        background: color-mix(in srgb, var(--accent-color), transparent 20%);
      }
    }
  }

  .portfolio-description {
    h2 {
      font-size: 26px;
      font-weight: 700;
      margin-bottom: 20px;
    }

    p {
      padding: 0;
    }

    .testimonial-item {
      padding: 30px 30px 0 30px;
      position: relative;
      background: color-mix(in srgb, var(--default-color), transparent 97%);
      margin-bottom: 50px;

      .testimonial-img {
        width: 90px;
        border-radius: 50px;
        border: 6px solid var(--background-color);
        float: left;
        margin: 0 10px 0 0;
      }

      h3 {
        font-size: 18px;
        font-weight: bold;
        margin: 15px 0 5px 0;
        padding-top: 20px;
      }

      h4 {
        font-size: 14px;
        color: #6c757d;
        margin: 0;
      }

      .quote-icon-left,
      .quote-icon-right {
        color: color-mix(in srgb, var(--accent-color), transparent 50%);
        font-size: 26px;
        line-height: 0;
      }

      .quote-icon-left {
        display: inline-block;
        left: -5px;
        position: relative;
      }

      .quote-icon-right {
        display: inline-block;
        right: -5px;
        position: relative;
        top: 10px;
        transform: scale(-1, -1);
      }

      p {
        font-style: italic;
        margin: 0 0 15px 0 0 0;
        padding: 0;
      }
    }
  }
}