
<?
  $id = $_GET['id'];
?>
<h1> Edit Images </h1>
<h2> Change or delete images </h2>
<br>

<?
echo"<a href='?p=gallery_image_add'>Add Images</a>";

 // connect to database
 include'../function.php';

echo"<div style='padding:20px; float:left; width:100%;'>";
//Get image from database..

 $get = mysql_query("SELECT * FROM images WHERE id ='$id'");

 while($rows = mysql_fetch_assoc($get))
 {

 $images   = $rows['image'];
 $tag      = $rows['id'];

            if(($images=='./images/')||($images=='../image/')||($images=='./image/')||($images==''))
{
  echo    "<img  src='../images/graph design/fl.png' width='125px' height='100px'>";
}
else
{echo"
          <div class='contentOptions'>

          <a href='../images/$images'>

           <img border='0' align='middle' width='200px' height='200px' title='$type' src='../images/$images'>
";}echo"
          </a>
          <div style='bottom:0; position:relative; padding:2px;'>
          
          <a href='?p=listing_image_modify_img&id=$id'><img src='../images/change.png' width='24'> Change</a>

          <a href='?p=listing_image_delete&id=$tag'><img src='../images/delete.png' width='24'>Delete</a>
          </div>
          </div>

";
 }


?>
</div>