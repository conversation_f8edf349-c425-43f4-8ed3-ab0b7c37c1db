{"version": 3, "file": "popover.js", "sources": ["../src/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.5.3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '4.5.3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const $tip = $(this.getTipElement())\n\n    // We use append for html objects to maintain js events\n    this.setElementContent($tip.find(SELECTOR_TITLE), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent($tip.find(SELECTOR_CONTENT), content)\n\n    $tip.removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Popover._jQueryInterface\n$.fn[NAME].Constructor = Popover\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Popover._jQueryInterface\n}\n\nexport default Popover\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "RegExp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "placement", "trigger", "content", "template", "DefaultType", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "Popover", "isWithContent", "getTitle", "_getContent", "addAttachmentClass", "attachment", "getTipElement", "addClass", "tip", "config", "<PERSON><PERSON><PERSON><PERSON>", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "call", "element", "removeClass", "getAttribute", "_cleanTipClass", "tabClass", "attr", "match", "length", "join", "_jQueryInterface", "each", "data", "_config", "test", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAG,SAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,YAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B;EACA,IAAMO,YAAY,GAAG,YAArB;EACA,IAAMC,kBAAkB,GAAG,IAAIC,MAAJ,aAAqBF,YAArB,WAAyC,GAAzC,CAA3B;;EAEA,IAAMG,OAAO,gBACRC,2BAAO,CAACD,OADA;EAEXE,EAAAA,SAAS,EAAE,OAFA;EAGXC,EAAAA,OAAO,EAAE,OAHE;EAIXC,EAAAA,OAAO,EAAE,EAJE;EAKXC,EAAAA,QAAQ,EAAE,yCACE,2BADF,GAEE,kCAFF,GAGE;EARD,EAAb;;EAWA,IAAMC,WAAW,gBACZL,2BAAO,CAACK,WADI;EAEfF,EAAAA,OAAO,EAAE;EAFM,EAAjB;;EAKA,IAAMG,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EAEA,IAAMC,cAAc,GAAG,iBAAvB;EACA,IAAMC,gBAAgB,GAAG,eAAzB;EAEA,IAAMC,KAAK,GAAG;EACZC,EAAAA,IAAI,WAASnB,SADD;EAEZoB,EAAAA,MAAM,aAAWpB,SAFL;EAGZqB,EAAAA,IAAI,WAASrB,SAHD;EAIZsB,EAAAA,KAAK,YAAUtB,SAJH;EAKZuB,EAAAA,QAAQ,eAAavB,SALT;EAMZwB,EAAAA,KAAK,YAAUxB,SANH;EAOZyB,EAAAA,OAAO,cAAYzB,SAPP;EAQZ0B,EAAAA,QAAQ,eAAa1B,SART;EASZ2B,EAAAA,UAAU,iBAAe3B,SATb;EAUZ4B,EAAAA,UAAU,iBAAe5B;EAVb,CAAd;EAaA;;;;;;MAMM6B;;;;;;;;;EA+BJ;WAEAC,gBAAA,yBAAgB;EACd,WAAO,KAAKC,QAAL,MAAmB,KAAKC,WAAL,EAA1B;EACD;;WAEDC,qBAAA,4BAAmBC,UAAnB,EAA+B;EAC7BhC,IAAAA,qBAAC,CAAC,KAAKiC,aAAL,EAAD,CAAD,CAAwBC,QAAxB,CAAoChC,YAApC,SAAoD8B,UAApD;EACD;;WAEDC,gBAAA,yBAAgB;EACd,SAAKE,GAAL,GAAW,KAAKA,GAAL,IAAYnC,qBAAC,CAAC,KAAKoC,MAAL,CAAY1B,QAAb,CAAD,CAAwB,CAAxB,CAAvB;EACA,WAAO,KAAKyB,GAAZ;EACD;;WAEDE,aAAA,sBAAa;EACX,QAAMC,IAAI,GAAGtC,qBAAC,CAAC,KAAKiC,aAAL,EAAD,CAAd,CADW;;EAIX,SAAKM,iBAAL,CAAuBD,IAAI,CAACE,IAAL,CAAU1B,cAAV,CAAvB,EAAkD,KAAKe,QAAL,EAAlD;;EACA,QAAIpB,OAAO,GAAG,KAAKqB,WAAL,EAAd;;EACA,QAAI,OAAOrB,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACgC,IAAR,CAAa,KAAKC,OAAlB,CAAV;EACD;;EAED,SAAKH,iBAAL,CAAuBD,IAAI,CAACE,IAAL,CAAUzB,gBAAV,CAAvB,EAAoDN,OAApD;EAEA6B,IAAAA,IAAI,CAACK,WAAL,CAAoB/B,eAApB,SAAuCC,eAAvC;EACD;;;WAIDiB,cAAA,uBAAc;EACZ,WAAO,KAAKY,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAKR,MAAL,CAAY3B,OADd;EAED;;WAEDoC,iBAAA,0BAAiB;EACf,QAAMP,IAAI,GAAGtC,qBAAC,CAAC,KAAKiC,aAAL,EAAD,CAAd;EACA,QAAMa,QAAQ,GAAGR,IAAI,CAACS,IAAL,CAAU,OAAV,EAAmBC,KAAnB,CAAyB7C,kBAAzB,CAAjB;;EACA,QAAI2C,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACG,MAAT,GAAkB,CAA3C,EAA8C;EAC5CX,MAAAA,IAAI,CAACK,WAAL,CAAiBG,QAAQ,CAACI,IAAT,CAAc,EAAd,CAAjB;EACD;EACF;;;YAIMC,mBAAP,0BAAwBf,MAAxB,EAAgC;EAC9B,WAAO,KAAKgB,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGrD,qBAAC,CAAC,IAAD,CAAD,CAAQqD,IAAR,CAAaxD,QAAb,CAAX;;EACA,UAAMyD,OAAO,GAAG,OAAOlB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACiB,IAAD,IAAS,eAAeE,IAAf,CAAoBnB,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACiB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI1B,OAAJ,CAAY,IAAZ,EAAkB2B,OAAlB,CAAP;EACAtD,QAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQqD,IAAR,CAAaxD,QAAb,EAAuBwD,IAAvB;EACD;;EAED,UAAI,OAAOjB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOiB,IAAI,CAACjB,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoB,SAAJ,wBAAkCpB,MAAlC,QAAN;EACD;;EAEDiB,QAAAA,IAAI,CAACjB,MAAD,CAAJ;EACD;EACF,KApBM,CAAP;EAqBD;;;;EAnGD;0BAEqB;EACnB,aAAOxC,OAAP;EACD;;;0BAEoB;EACnB,aAAOS,OAAP;EACD;;;0BAEiB;EAChB,aAAOV,IAAP;EACD;;;0BAEqB;EACpB,aAAOE,QAAP;EACD;;;0BAEkB;EACjB,aAAOmB,KAAP;EACD;;;0BAEsB;EACrB,aAAOlB,SAAP;EACD;;;0BAEwB;EACvB,aAAOa,WAAP;EACD;;;;IA7BmBL;EAuGtB;;;;;;;AAMAN,uBAAC,CAACC,EAAF,CAAKN,IAAL,IAAagC,OAAO,CAACwB,gBAArB;AACAnD,uBAAC,CAACC,EAAF,CAAKN,IAAL,EAAW8D,WAAX,GAAyB9B,OAAzB;;AACA3B,uBAAC,CAACC,EAAF,CAAKN,IAAL,EAAW+D,UAAX,GAAwB,YAAM;EAC5B1D,EAAAA,qBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAO4B,OAAO,CAACwB,gBAAf;EACD,CAHD;;;;;;;;"}