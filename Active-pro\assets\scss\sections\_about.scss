// main: ../main.scss
/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about {
  .btn-get-started {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    border-radius: 30px;
    padding: 8px 30px;
    border: 2px solid transparent;
    transition: 0.3s all ease-in-out;
    font-size: 14px;

    &:hover {
      border-color: var(--accent-color);
      background-color: transparent;
      color: var(--accent-color);
    }
  }

  h1 {
    color: var(--heading-color);
    font-size: 30px;
  }

  p {
    line-height: 1.7;
    color: var(--default-color);
  }

  .swiper-pagination {
    position: absolute;
    bottom: 30px;

    .swiper-pagination-bullet {
      margin: 0 5px;
      background: #ffffff;
      opacity: 0.3;

      &.swiper-pagination-bullet-active {
        background: var(--accent-color);
        opacity: 1;
      }
    }
  }

  .section-subtitle {
    font-size: 15px;
    margin-bottom: 10px;
    display: block;
    color: var(--default-color);
  }
}