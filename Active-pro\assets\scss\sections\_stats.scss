// main: ../main.scss
/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats {
  padding-top: 60px;

  .content-subtitle {
    font-size: 15px;
    margin-bottom: 10px;
    display: block;
    color: var(--default-color);
  }

  .lead {
    line-height: 1.6;
    font-size: 18px;
    font-weight: normal;
    color: var(--default-color);
  }

  .content-title {
    color: var(--heading-color);
    font-size: 22px;
    margin-bottom: 30px;
  }

  p {
    line-height: 1.7;
    color: var(--default-color);
  }

  .btn-get-started {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    border-radius: 30px;
    padding: 8px 30px;
    border: 2px solid transparent;
    transition: 0.3s all ease-in-out;
    font-size: 14px;

    &:hover {
      border-color: var(--accent-color);
      background-color: transparent;
      color: var(--accent-color);
    }
  }

  .count-numbers {
    .number {
      font-size: 1.8rem;
      color: var(--heading-color);
      position: relative;
      display: block;
      padding-bottom: 7px;
      margin-bottom: 10px;

      &:after {
        content: "";
        left: 0;
        bottom: 0;
        position: absolute;
        width: 20px;
        height: 2px;
        background: var(--accent-color);
      }
    }
  }
}