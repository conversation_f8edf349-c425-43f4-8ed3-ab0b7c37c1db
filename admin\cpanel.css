body {
    font-family: <PERSON><PERSON><PERSON>, Arial, Helvetica, sans-serif;
    font-size: 12px;
    margin:0px;
    background: #fff;
}

#wrapper
{
    margin: 20px auto;
    overflow-x: hidden;
    overflow-y: hidden;
    width:980px;
    padding:10px;
    background-color:transparent;

}
.main-content{
 margin: 0 auto;
 width:790px;
 min-height:600px;
}
.right-menu
{

}

.li{
    border-bottom: 1px solid lightgray;
    height: 40px;
    padding:7px;
    width: 125px;
    list-style-type:none;
}
ul, li{list-style-type:none;}

/* Social Network icons */

#name
{
    float: right;
    left: 0px;
    position: relative;
    top: 14px;
}

 /* Social Network icons -- End --*/

#img{
    position: absolute;
    right: 0;
    z-index: 1;
}
#img2{
    position: absolute;
    left: 0;
    z-index: 1;
}

.product-info {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 385px;
    margin-top: 0;
}

#back
{
    left: 50px;
    margin-left: 0px;
    margin-top: 5px;
}


/**************************************
Button
**************************************/

#button 
{
    left: 50px;
    margin-left: 295px;
    margin-top: 5px;
}

.button 
{

  -o-border-radius:        3px;
  -moz-border-radius:      3px;
  -webkit-border-radius:   3px;
  background:              white url('../images/button.png') 0 0 repeat-x; /* Image fallback */
  background:             -ms-linear-gradient(0% 170% 90deg, #c4c4c4, white);
  background:             -moz-linear-gradient(0% 170% 90deg, #c4c4c4, white);
  background:             -webkit-gradient(linear, 0% 0%, 0% 170%, from(white), to(#c4c4c4));
  border:                  1px solid #BFDBFF;
  border-color:            #e6e6e6 #cccccc #cccccc #e6e6e6;
  border-radius:           3px;
  color:                   #000;
  display:                 inline-block;
  font-family:            "helvetica neue", helvetica, arial, freesans, "liberation sans", "numbus sans l", sans-serif;
  font-size:               13px;
  outline :                 0;
  padding:                 5px 8px 5px;
  text-align:              center;
  text-decoration:         none;
  text-shadow:             1px 1px 0 white;
  white-space:             nowrap;
  overflow-x:auto;
  overflow-y:auto;
}

  .button:hover {
    background:           -ms-linear-gradient(0% 170% 90deg, #b8b8b8, white);
    background:           -moz-linear-gradient(0% 170% 90deg, #b8b8b8, white);
    background:           -webkit-gradient(linear, 0% 0%, 0% 170%, from(white), to(#b8b8b8));
    border-color:          #BFDBFF;
    color:                 #333333; }

  .button:active {
    position:              relative;
    top:                   1px; }

  .button:active, .button:focus {
    background-position:   0 -25px;
    background:           -ms-linear-gradient(0% 170% 90deg, white, #dedede);
    background:           -moz-linear-gradient(0% 170% 90deg, white, #dedede);
    background:           -webkit-gradient(linear, 0% 0%, 0% 170%, from(#dedede), to(white));
    border-color:          #8fc7ff #94c9ff #94c9ff #8fc7ff;
    color:                 #1a1a1a;
    text-shadow:           1px -1px 0 rgba(255, 255, 255, 0.5); }

.message {
    margin-left: 58px;
    min-height: 65px;
}
.message-image {
    float: left;
    height: 48px;
    margin-top: 3px;
    overflow-x: hidden;
    overflow-y: hidden;
    width: 48px;
}
.message-row {
    display: block;
    line-height: 15px;
    position: relative;
}

.menu
{
  float:left;
  position:absolute;
  top:3px;
}

.content .propTekst dt {
	clear:   left;
	display:  block;
	font-weight: bold;
	float:   left;
	width:   125px;
	padding: 2px;
}

.content .propTekst dd {
	clear:   right;
	display:  block;
	white-space: nowrap;
	padding: 2px;
}

.resultItem .addTekst a.cat, .resultItem .propTekst a.cat, .newsitem a.cat, .content .propTekst a.cat{
	color:black;
	max-width:30em;
}
a.cat1
{
  color:green;

}
.resultItem .addTekst ul, .resultItem .propTekst ul, .content .propTekst ul{
	list-style:none;
	padding:5px 0 10px 0;
}
.resultItem .addTekst p.review, .resultItem .propTekst p.review{
	border-left: 1px;
	padding:10px;
	font-style:italic;
}

.content p.realtorInfo {
	clear: left;
	margin-left: 130px;
	border-left: #f26522 1px;
	padding:10px;
	font-style:italic;
}

.resultItem .addTekst p.review a, .resultItem .propTekst p.review a {
	color: #333333;
}

.content p.realtorInfo a {
	color: #333333;
}

.resultItem .addTekst ul li.closetag { line-height: 26px; color:#bbbbbb; font-size:14px; font-weight:bold; }
.resultItem .addTekst ul li.closetag .closetxt { color:#ff0000; }
.resultItem .addTekst ul li.closed { color:#bbbbbb; }

.propTekst p.price{
	padding:10px;
	font-style:italic;
	font-weight: bold;
	color: red;
}

.content .propPrice {
	float: left;
	width: 100px;
	font-weight: bold;
	color: red;
}

.resultItem .propImages {
	width:260px;
	height:auto;
	float:right;
	padding:0 0 0 20px;
}

.content .propImages {
	width:170px;
	height:auto;
	float:right;
	padding:0 0 0 20px;
}

.resultItem .propImages img, .content .propImages img {
	padding: 2px;
}


a, * a {
    color:  #3B5998;
    text-decoration: none;
        }
table.tagchangetable {
    border-bottom-color: #DDDDDD;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    margin-bottom: 10px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
}
table.tagchangetable .icontd {
    width: 190px;
}
table .tagstd {
    width: 255px !important;
}
.tagsarrow {
    padding-top: 60px;
    text-align: right;
    width: 30px;
}
table .tagstd, table .icontd, table .votetd {
    padding-bottom: 15px;
    padding-left: 0;
    padding-right: 0;
    padding-top: 15px;
}
table .tagstd, table .votetd {
    padding-left: 10px;
}
table .votetd {
}
table.tagchangetable ul li {
    border-bottom-color: #FAFAFA;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 5px;
}
table.tagchangetable ul.existing, table.tagchangetable ul.deleted, table.tagchangetable ul.added {
}
table.tagchangetable ul.existing {
    background-color: #FFFFFF;
}
table.tagchangetable ul.deleted {
    background-color: #F7C5B7;
    font-weight: bold;
}
table.tagchangetable ul.added {
    background-color: #C5F7B7;
    font-weight: bold;
}
span.total {
    clear: both;
    display: block;
    padding-bottom: 5px;
    padding-left: 0;
    padding-right: 0;
    padding-top: 5px;
}

#loginarea {
    -moz-border-bottom-colors: none;
    -moz-border-image: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #F1F1F1;
    background-position: center bottom;
    background-repeat: repeat-x;
    box-shadow:1px 1px 1px 1px lightgray;
    line-height: 18px;
    margin-bottom: 50px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 5px;
    padding-bottom: 12px;
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 20px;
    width: 450px;
}
#loginarea2 {
    -moz-border-bottom-colors: none;
    -moz-border-image: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #FFF;
    background-image: url("../../images/grad.png");
    background-position: center bottom;
    background-repeat: repeat-x;
    box-shadow: 1px 1px 1px 2px #EAEDF4;
    line-height: 28px;
    margin-bottom: 50px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 5px;
    padding-bottom: 12px;
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 20px;
    width: 363px;

}
#loginarea, #loginarea input {
}
#loginarea td, #editaccount td, #loginarea th, #editaccount th {
    padding-bottom: 15px;
    vertical-align: middle;
}
th {
    padding-right: 20px;
    text-align: right;
}
#loginarea tr {
    vertical-align: middle;
}
#loginarea label {
}
.product {
    margin-bottom: 38px;
    margin-left: 0;
    margin-right: 0;
    margin-top: -8px;
}
.col-1 {
    height: 42px;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 18px;
    margin-top: 10px;
    width: 42px;
}
h4.product-name {
    font-size: 1.09em;
    font-weight: normal;
    margin-bottom: 0.75em;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    white-space: nowrap;
}
.col-4 {
    width: 300px;
}
h5.featured-title {
    color: #777777;
    font-size: 0.78em;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    margin-top: 10px;
    text-transform: uppercase;
}
bizsol-new.css (line 11)
h4, h5, h6, table {
    font-size: inherit;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
}

input#inputField {

 box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.95) inset;

font-size: 16px;
}
input#inputField, .stylefield {
    -moz-border-bottom-colors: none;
    -moz-border-image: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #FFFFFF;
    background-image: url("/design/images/gradient.pn");
    background-position: left top;
    background-repeat: repeat-x;
    border-bottom-color: #9EB7CD;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-left-color: #9EB7CD;
    border-left-style: solid;
    border-left-width: 1px;
    border-right-color: #9EB7CD;
    border-right-style: solid;
    border-right-width: 1px;
    border-top-color: #9EB7CD;
    border-top-style: solid;
    border-top-width: 1px;
    font-size: 12px;
    font-weight: normal;
    height: 20px;
    padding-bottom: 3px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 3px;
    width: 65%;
   border-top-left-radius: 5px;
   border-bottom-left-radius: 5px;
}

#login {
    float: right;
    margin:0;
    text-align: left !important;
    width: 100%;
    top: 0px;
    position: relative;
}
#login li {
    float: right !important;
}
#view-search #login ul, #view-icondetails #login ul {
    position: absolute;
    right: 15px;
    text-align: right;
    top: 5px;
}
#view-search #login ul li, #view-icondetails #login ul li {
    display: inline;
    list-style-type: none;
    text-align: right;
}
#login-form {
    display: none;
}
#menu #subscribe {
    bottom: 33px;
    left: 390px;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    position: relative;
    width: 200px;
}
#menu #subscribe a {
    display: block;
    float: right;
    height: 20px;
    width: 20px;
}
#subscribe a {
}
#subscribe a:hover {
    background-color: #6A8FB0 !important;
}
#menu ul li, #smallmenu ul li {
    float: left;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
}
#menu ul li a, #smallmenu ul li a {
    display: block;
    float: left;
    height: 22px;
    padding-bottom: 0;
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 3px;
}
#menu a:hover {
    color:#6A8FB0;
}
#menu a:active {
    color:#000 ;
}
#menu li a:hover {
    background-color: ;

}
#menu li a:active {
    color: #000;
}
#menu li.selected a {
    color: #FF6820;
    font-weight: bold;
}
#subwrapper {
    height: 32px;
}
#submenu {
    float: left;
    height: 22px;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    width: 667px;
    position:absolute;
}
#submenu ul li, #login ul li {
}
#login ul li.selected {
    background-color:;
}
#submenu li.selected a {
}
#subsubmenu {
    clear: both;
    height: 33px;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    max-width: 990px;
}
#subsubmenu li {
    height: 44px;
}
ul#subsubmenu li a {
    background-color: white !important;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    margin-top: 3px;
    padding-bottom: 3px;
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 2px;
}
ul#subsubmenu li a:hover {
    color: black;
}
#menu-content li {
    width: 180px;
}
#menu-content li a {
    cursor: pointer;
    display: block;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 5px;
    width: 154px;
}
#menu-content li a:hover {
    background-color: #fff;
}
#menu-content li a:active {
    background-color: #A5A5A5;
}
#smallmenu {
}
#smallmenu a#dropmenu {
    background-image: url("/design/images/submenu.png");
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 32px;
    left: 190px;
    position: absolute;
    top: 25px;
    width: 23px;
}
#smallmenu #menu-content {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    display: none;
    left: 100px;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 10px;
    margin-top: -25px;
    padding-bottom: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 0 !important;
    position: absolute;
    width: 180px;
    z-index: 999;
}
#smallmenu #menu-content, #smallmenu #menu-content ul {
    list-style-type: none;
}
#smallmenu #menu-content li {
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 0 !important;
    width: 180px;
}
#smallmenu #menu-content li a {
    height: 20px;
    line-height: 100%;
    padding-bottom: 0 !important;
    padding-left: 10px !important;
    padding-right: 0 !important;
    padding-top: 10px !important;
    width: 170px;
}

/* Profile header Search Section*/

#header {
    background-attachment: scroll;
    background-clip: border-box;
    background-color: #F1F1F1;
    background-image:url(../images/backgray.png);
    background-origin: padding-box;
    background-position: 0 0;
    background-repeat: repeat;
    background-size: auto auto;
    border-bottom-color: lightgray;
    border-bottom-style: dotted;
    border-bottom-width: 1px;
    clear: both;
    height: 58px;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    padding-left: 0;
    padding-top: 0;
    width: 100%;
    z-index: 5;
}

#logo {
    float: left;
    height: 65px;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 20px;
    margin-top: 12px;
    width: 172px;
}

#searcharea {
    float: left;
    margin-bottom: 0;
    margin-left: 30px;
    margin-right: 0;
    margin-top: 12px;
    padding-bottom: 6px;
    padding-left: 0;
    padding-right: 0;
    padding-top: 6px;
    text-align: center;
    width: 60%;
}
        /* Suggestion Box */
	h3 {
		margin: 0px;
		padding: 0px;
	}

	.suggestionsBox {
		position: relative;
		left: 30px;
		margin: 10px 0px 0px 0px;
		width: 200px;
		background-color: #fff;
		  box-shadow: 2px 2px 2px #EBEBEB;
		color: #000;
		font-size: 12px;
		z-index:25;
	}

	.suggestionList {
		margin: 0px;
		padding: 0px;
	}

	.suggestionList li {

		margin: 0px 0px 3px 0px;
		padding: 3px;
		cursor: none;
	}

	.suggestionList li:hover {
		background-color: #659CD8;
	}

/* Profile Table */

.coltable {
    clear: both;
    margin-bottom: 0 !important;
    margin-left: auto !important;
    margin-right: auto !important;
    margin-top: 0 !important;
    width: 813px;
}
.contenttd {
    line-height: 125%;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 20px;
    padding-top: 0;
}

h1 {
    font-weight: bold;
}

h1, h1 * {
    font-size: 24px;
    line-height: 115%;
    margin-bottom: 10px !important;
    padding-bottom: 5px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 15;
}
h2 {
    color: #2F4A90;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
}
th{
    padding-right: 20px;
    text-align: right;
}

table tr {
    vertical-align: top;
}

table {
    border-collapse: collapse;
}

 .chartcell {
    padding-left: 30px;
    width: 480px;
}


.texttable td {
    padding-left: 0px;
}
.chartcell {
    padding-left: 30px;
    width: 480px;
} /* Menu Structure */
#contentbody {
    float: left;
    width: 1000px;
}
#contentModOptions {
    float: left;
    position: relative;
    width: 185px;
}
#contentModOptions ul {
    list-style-image: none;
    list-style-position: outside;
    list-style-type: none;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
}
#contentModOptions li {
    padding-bottom: 4px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 4px;
}

#contentmanagermain {
    float: left;
    margin-bottom: 10px;
    margin-left: 10px;
    margin-right: 0;
    margin-top: 0;
    width: 1000px;
}
.contentOptionRow {
    clear: both;
    position: relative;
    width: 100%;
}
.contentOption {
    box-shadow: 2px 2px 2px 0 lightgray;
    float: left;
    margin-bottom: 8px;
    margin-left: 4px;
    margin-right: 8px;
    margin-top: 8px;
    padding: 3px;
    position: relative;
    text-align: center;
    width: 23%;
    border:;
    border-radius: 1px;

}

 .contentOptions {
    box-shadow:0px 0px 1px #f1f1f1;
    float: left;
    margin-bottom: 8px;
    margin-left: 4px;
    margin-right: 8px;
    margin-top: 8px;
    padding: 6px;
    position: relative;
    text-align: center;
    width:20%;
    background-image: -webkit-linear-gradient(top, #FFFFFF 0%, #FFF 100%);
    background-image: -moz-linear-gradient(top, #FFFFFF 0%, #FFF 100%);
    background-image: -ms-linear-gradient(top, #FFFFFF 0%, #FFF 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFF', endColorstr='#FFF');
    border: solid 0px #499dd3;
    border-radius: 0px;

}

#content_menuOptions {
    background-color: #8DC63F;
    margin: 10px 0px;
    overflow: auto;
    position: relative;
    width: 100%;
}
#content_menuOptions ul {
    list-style-image: none;
    list-style-position: outside;
    list-style-type: none;
    margin: 0;
    Padding: 0;
}
#content_menuOptions li {
    float: left;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 5px;
}

.cp-menu{padding-bottom:5px; border-bottom:1px dotted lightgray;}



/*********************************************
 Alerts
**********************************************/

.warning {
  background-color:#FFFFDF;
  border-bottom-color:#FDFF3F;
  border-bottom-style:solid;
  border-bottom-width:1px;
  border-top-color:#FDFF3F;
  border-top-style:solid;
  border-top-width:1px;
  margin-bottom:10px;
  margin-left:0;
  margin-right:0;
  margin-top:0;
  padding-bottom:10px;
  padding-left:10px;
  padding-right:10px;
  padding-top:10px;
  z-index:5;
}

 .product-info {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 385px;
    margin-top: 0;
}
.clearfix:after {
    clear: both;
    content: ".";
    display: block;
    font-size: 0;
    height: 0;
    visibility: hidden;
}
.main {
    max-width: 1000px;
    min-width: 780px;
    width: auto;
}
.main {
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    padding-bottom: 100px;
    padding-top: 23px;
    width: 875px;
}
.content {
    padding-bottom: 0;
    padding-left: 4px;
    padding-right: 4px;
    padding-top: 0;
}
 #loginarea input[type="password"], #loginarea input[id="Username"], input[type="text"], input[type="password"] {
    direction: ltr;
    font-size: 14px;
    height: 24px;
    width: 32%;
    padding:5px;

}
td{
 border:1px dotted #f1f1f1;
 background-color:;
 height:10px;
 width:12px;
 text-align:center;
 padding:5px;
}
td:hover{

 background-color:#f1f1f1;
 cursor: ;
}

#main-navigation h4 {
    border-bottom-color: #EFEFF0;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    color: #499dd3;
    font-size: 14px;
    font-weight: bold;
    line-height: 19.5px;
    margin-bottom: 0;
    padding-bottom: 5px;
    padding-left: 18px;
    padding-right: 18px;
    padding-top: 0;
    text-transform: uppercase;
}
#main-navigation a {
    
    line-height: 24px;
    padding-bottom: 9px;
    padding-left: 18px;
    padding-right: 18px;
    padding-top: 9px;
}
#main-navigation a {
    -moz-text-blink: none;
    -moz-text-decoration-color: -moz-use-text-color;
    -moz-text-decoration-line: none;
    -moz-text-decoration-style: solid;
    font-size: 13px;
    line-height: 20px;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    width: 144px;
}
#main-navigation .icon-if {
    position: relative;
    top: 6px;
}
#main-navigation a:hover {
    background-color: #F3F3F4;
    color: #3A3A3B;
}
#main-navigation .nav {
    margin-bottom: 18px;
    margin-top: 0;
}
#main-navigation ul {
 
}
#main-navigation li {
    display: inline-block;
    list-style-type: none;
 
}
#main-navigation li.active a {
    background-color: ;
    color:;
}.nav {
    list-style-image: none;
    list-style-position: outside;
    list-style-type: none;
    margin-bottom: 18px;
    margin-left: 0;
}
.nav > li > a {
    display: block;
}
.nav > li > a:hover {
    -moz-text-blink: none;
    -moz-text-decoration-color: -moz-use-text-color;
    -moz-text-decoration-line: none;
    -moz-text-decoration-style: solid;
    background-color: #EFEFF0;
}
.nav > .pull-right {
    float: right;
}
.nav .nav-header {
    color: #DFDFE0;
    display: block;
    font-size: 11px;
    font-weight: bold;
    line-height: 18px;
    padding-bottom: 3px;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 3px;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    text-transform: uppercase;
}
.nav li + .nav-header {
    margin-top: 9px;
}
.nav-list {
    margin-bottom: 0;
    padding-left: 15px;
    padding-right: 15px;
}
.nav-list > li > a, .nav-list .nav-header {
    margin-left: -15px;
    margin-right: -15px;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}
.nav-list > li > a {
    padding-bottom: 3px;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 3px;
}
.nav-list > .active > a, .nav-list > .active > a:hover {
    background-color: #447BC6;
    color: #FFFFFF;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.2);
}
.nav-list [class^="icon-"] {
    margin-right: 2px;
}
.nav-list .divider {
    background-color: #E5E5E5;
    border-bottom-color: #FFFFFF;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    height: 1px;
    margin-bottom: 8px;
    margin-left: 1px;
    margin-right: 1px;
    margin-top: 8px;
    overflow-x: hidden;
    overflow-y: hidden;
}
.nav-tabs, .nav-pills {
}
.nav-tabs:before, .nav-pills:before, .nav-tabs:after, .nav-pills:after {
    content: "";
    display: table;
}
.nav-tabs:after, .nav-pills:after {
    clear: both;
}
.nav-tabs > li, .nav-pills > li {
    float: left;
}
.nav-tabs > li > a, .nav-pills > li > a {
    line-height: 14px;
    margin-right: 2px;
    padding-left: 12px;
    padding-right: 12px;
}
.nav-tabs {
    border-bottom-color: #DDDDDD;
    border-bottom-style: solid;
    border-bottom-width: 1px;
}
.nav-tabs > li {
    margin-bottom: -1px;
}
.nav-tabs > li > a {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-bottom-color: transparent;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-image-outset: 0 0 0 0;
    border-image-repeat: stretch stretch;
    border-image-slice: 100% 100% 100% 100%;
    border-image-source: none;
    border-image-width: 1 1 1 1;
    border-left-color-ltr-source: physical;
    border-left-color-rtl-source: physical;
    border-left-color-value: transparent;
    border-left-style-ltr-source: physical;
    border-left-style-rtl-source: physical;
    border-left-style-value: solid;
    border-left-width-ltr-source: physical;
    border-left-width-rtl-source: physical;
    border-left-width-value: 1px;
    border-right-color-ltr-source: physical;
    border-right-color-rtl-source: physical;
    border-right-color-value: transparent;
    border-right-style-ltr-source: physical;
    border-right-style-rtl-source: physical;
    border-right-style-value: solid;
    border-right-width-ltr-source: physical;
    border-right-width-rtl-source: physical;
    border-right-width-value: 1px;
    border-top-color: transparent;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-top-style: solid;
    border-top-width: 1px;
    line-height: 18px;
    padding-bottom: 8px;
    padding-top: 8px;
}
.nav-tabs > li > a:hover {
    border-bottom-color: #DDDDDD;
    border-left-color-ltr-source: physical;
    border-left-color-rtl-source: physical;
    border-left-color-value: #EFEFF0;
    border-right-color-ltr-source: physical;
    border-right-color-rtl-source: physical;
    border-right-color-value: #EFEFF0;
    border-top-color: #EFEFF0;
}
.nav-tabs > .active > a, .nav-tabs > .active > a:hover {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #FFFFFF;
    border-bottom-color: transparent;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-image-outset: 0 0 0 0;
    border-image-repeat: stretch stretch;
    border-image-slice: 100% 100% 100% 100%;
    border-image-source: none;
    border-image-width: 1 1 1 1;
    border-left-color-ltr-source: physical;
    border-left-color-rtl-source: physical;
    border-left-color-value: #DDDDDD;
    border-left-style-ltr-source: physical;
    border-left-style-rtl-source: physical;
    border-left-style-value: solid;
    border-left-width-ltr-source: physical;
    border-left-width-rtl-source: physical;
    border-left-width-value: 1px;
    border-right-color-ltr-source: physical;
    border-right-color-rtl-source: physical;
    border-right-color-value: #DDDDDD;
    border-right-style-ltr-source: physical;
    border-right-style-rtl-source: physical;
    border-right-style-value: solid;
    border-right-width-ltr-source: physical;
    border-right-width-rtl-source: physical;
    border-right-width-value: 1px;
    border-top-color: #DDDDDD;
    border-top-style: solid;
    border-top-width: 1px;
    color: #BBBBBC;
    cursor: default;
}
.nav-pills > li > a {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    margin-bottom: 2px;
    margin-top: 2px;
    padding-bottom: 8px;
    padding-top: 8px;
}
.nav-pills > .active > a, .nav-pills > .active > a:hover {
    background-color: #FFFFFF;
    color: #FFFFFF;
    font-weight: bold;
}
.nav-stacked > li {
    float: none;
}
.nav-stacked > li > a {
    margin-right: 0;
}
.nav-tabs.nav-stacked {
    border-bottom-color: -moz-use-text-color;
    border-bottom-style: none;
    border-bottom-width: 0;
}
.nav-tabs.nav-stacked > li > a {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-bottom-color: #DDDDDD;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-image-outset: 0 0 0 0;
    border-image-repeat: stretch stretch;
    border-image-slice: 100% 100% 100% 100%;
    border-image-source: none;
    border-image-width: 1 1 1 1;
    border-left-color-ltr-source: physical;
    border-left-color-rtl-source: physical;
    border-left-color-value: #DDDDDD;
    border-left-style-ltr-source: physical;
    border-left-style-rtl-source: physical;
    border-left-style-value: solid;
    border-left-width-ltr-source: physical;
    border-left-width-rtl-source: physical;
    border-left-width-value: 1px;
    border-right-color-ltr-source: physical;
    border-right-color-rtl-source: physical;
    border-right-color-value: #DDDDDD;
    border-right-style-ltr-source: physical;
    border-right-style-rtl-source: physical;
    border-right-style-value: solid;
    border-right-width-ltr-source: physical;
    border-right-width-rtl-source: physical;
    border-right-width-value: 1px;
    border-top-color: #DDDDDD;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top-style: solid;
    border-top-width: 1px;
}
.nav-tabs.nav-stacked > li:first-child > a {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.nav-tabs.nav-stacked > li:last-child > a {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.nav-tabs.nav-stacked > li > a:hover {
    border-bottom-color: #DDDDDD;
    border-left-color-ltr-source: physical;
    border-left-color-rtl-source: physical;
    border-left-color-value: #DDDDDD;
    border-right-color-ltr-source: physical;
    border-right-color-rtl-source: physical;
    border-right-color-value: #DDDDDD;
    border-top-color: #DDDDDD;
    z-index: 2;
}
.nav-pills.nav-stacked > li > a {
    margin-bottom: 3px;
}
.nav-pills.nav-stacked > li:last-child > a {
    margin-bottom: 1px;
}